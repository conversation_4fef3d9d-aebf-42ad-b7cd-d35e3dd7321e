{"vue3snippets.enable-compile-vue-file-on-did-save-code": true, "vetur.validation.template": false, "vetur.validation.script": false, "vetur.validation.style": false, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "files.associations": {"*.vue": "vue"}, "emmet.includeLanguages": {"vue-html": "html", "vue": "html"}, "html.customData": [".vscode/element-plus.html-data.json"]}