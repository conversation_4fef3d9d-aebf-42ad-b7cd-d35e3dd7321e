<template>
  <div class="grouped-results-container">
    <div v-for="(group, groupKey) in groupedResults" :key="groupKey" class="group-container">
      <div class="group-header" @click="$emit('toggle-group', groupKey)">
        <div class="group-title">
          <span>🔍</span>
          <div class="keyword-combination">
            <span v-for="keyword in group.keywords" :key="keyword" class="combination-tag">
              {{ keyword }}
            </span>
          </div>
        </div>
        <div class="group-meta">
          <div class="group-count">{{ group.total_count || 0 }} 条记录</div>
          <div class="source-summary">
            <span v-if="group.sources && group.sources.files_v2 && group.sources.files_v2.count > 0" 
                  class="source-tag files-v2">
              文件: {{ group.sources.files_v2.count }}
            </span>
            <span v-if="group.sources && group.sources.text && group.sources.text.count > 0" 
                  class="source-tag text">
              文本: {{ group.sources.text.count }}
            </span>
          </div>
          <button class="group-toggle-btn" :class="{ collapsed: !groupExpandedState[groupKey] }">
            ▼
          </button>
        </div>
      </div>
      
      <div v-show="groupExpandedState[groupKey]" class="group-content">
        <template v-for="(sourceData, sourceKey) in (group.sources || {})" :key="sourceKey">
          <div v-if="sourceData && sourceData.count > 0" class="source-section">
            <div class="source-header">
              <div class="source-title">
                <span v-if="sourceKey === 'files_v2'">📄</span>
                <span v-else>📝</span>
                {{ sourceData.source_name || sourceKey }}
                <span class="source-count">({{ sourceData.count || 0 }} 条)</span>
              </div>
              <button class="source-toggle-btn" 
                      @click="$emit('toggle-source', { groupKey, sourceKey })" 
                      :class="{ collapsed: !getSourceExpandedState(groupKey, sourceKey) }">
                ▼
              </button>
            </div>

            <div v-show="getSourceExpandedState(groupKey, sourceKey)" class="source-content">
              <el-table
                :data="sourceData.records || []"
                style="width: 100%"
                :header-cell-style="{ background: '#f8f9fa', color: '#2c3e50' }"
                stripe>
                
                <el-table-column prop="id" label="ID" width="80" align="center">
                  <template #default="scope">
                    <el-tag type="info" size="small">{{ scope.row.id }}</el-tag>
                  </template>
                </el-table-column>
                
                <el-table-column prop="title" label="标题" min-width="300">
                  <template #default="scope">
                    <div class="title-cell" @click="$emit('view-record', scope.row)" :title="scope.row.title" v-html="scope.row.title_highlighted || scope.row.title"></div>
                  </template>
                </el-table-column>

                <el-table-column prop="content" label="内容预览" min-width="400">
                  <template #default="scope">
                    <div class="content-preview"
                         :title="scope.row.dynamic_content || scope.row.content"
                         v-html="scope.row.dynamic_content_highlighted || scope.row.content_highlighted || scope.row.dynamic_content || scope.row.content">
                      <div v-if="scope.row.content_source" class="content-source-tag">
                        {{ scope.row.content_source === 'files_v2' ? '文件内容' : '文本内容' }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column prop="keyword" label="前端展示" width="120">
                  <template #default="scope">
                    <span class="keyword-tag">{{ scope.row.keywordShow }}</span>
                  </template>
                </el-table-column>
                
                <el-table-column prop="MonitorDate" label="发布日期" width="120">
                  <template #default="scope">
                    <span class="date-badge">{{ scope.row.MonitorDate }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop="content_source" label="内容来源" width="100">
                  <template #default="scope">
                    <el-tag v-if="scope.row.content_source"
                            size="small"
                            :class="scope.row.content_source === 'files_v2' ? 'content-source-files' : 'content-source-text'"
                            :type="scope.row.content_source === 'files_v2' ? 'primary' : 'info'">
                      {{ scope.row.content_source === 'files_v2' ? 'FILES' : 'TEXT' }}
                    </el-tag>
                    <span v-else style="color: #bdc3c7;">-</span>
                  </template>
                </el-table-column>

                <el-table-column prop="createdAt" label="创建时间" width="120">
                  <template #default="scope">
                    <div class="date-time-cell">
                      <el-icon><Clock /></el-icon>
                      <span class="date-badge">{{ scope.row.createdAt }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="translated_time" label="翻译时间" width="120">
                  <template #default="scope">
                    <div class="date-time-cell">
                      <el-icon><Clock /></el-icon>
                      <span class="translated-time-badge">{{ formatTranslatedTime(scope.row.translated_time) }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="匹配关键词" width="150">
                  <template #default="scope">
                    <div class="keyword-combination">
                      <span v-for="keyword in scope.row.matched_keywords" 
                            :key="keyword" 
                            class="combination-tag small">
                        {{ keyword }}
                      </span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="150" align="center">
                  <template #default="scope">
                    <div class="action-buttons">
                      <el-button
                        type="primary"
                        size="small"
                        @click="$emit('view-record', scope.row)">
                        <el-icon><View /></el-icon>
                        详情
                      </el-button>
                      <el-button
                        v-if="scope.row.url"
                        type="success"
                        size="small"
                        @click="$emit('open-url', scope.row.url)"
                        link>
                        <el-icon><Link /></el-icon>
                        原文
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  groupedResults: {
    type: Object,
    required: true
  },
  groupExpandedState: {
    type: Object,
    required: true
  },
  sourceExpandedState: {
    type: Object,
    required: true
  }
})

defineEmits(['toggle-group', 'toggle-source', 'view-record', 'open-url'])

const getSourceExpandedState = (groupKey, sourceKey) => {
  try {
    return props.sourceExpandedState &&
           props.sourceExpandedState[groupKey] &&
           props.sourceExpandedState[groupKey][sourceKey];
  } catch (error) {
    console.warn('获取来源展开状态时出错:', error);
    return true; // 默认展开
  }
}

// 格式化翻译时间
const formatTranslatedTime = (translatedTime) => {
  if (!translatedTime) return '-';
  try {
    // 如果是ISO格式的时间字符串，格式化为可读格式
    if (typeof translatedTime === 'string' && translatedTime.includes('T')) {
      const date = new Date(translatedTime);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
    return translatedTime;
  } catch (e) {
    return translatedTime;
  }
}
</script>

<style scoped>
.group-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.title-cell {
  font-weight: 500;
  color: #2c3e50;
}

.combination-tag.small {
  font-size: 0.75em;
}
</style>