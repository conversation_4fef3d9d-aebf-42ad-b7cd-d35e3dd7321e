<template>
  <div class="query-form-wrapper">
    <div class="query-form">
      <h3 class="form-title">
        <el-icon><Search /></el-icon>
        查询参数设置
      </h3>
      
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="搜索关键词">
            <el-input
              v-model="formData.searchKeyword"
              placeholder="请输入搜索关键词，多个关键词用逗号分隔"
              clearable
              prefix-icon="Search">
            </el-input>
          </el-form-item>
        </div>

        <div class="form-item">
          <el-form-item label="ID搜索">
            <el-input
              v-model="formData.idSearch"
              placeholder="请输入ID，多个ID用逗号分隔"
              clearable
              prefix-icon="Key">
            </el-input>
          </el-form-item>
        </div>
      </div>

      <div class="form-row">
        <div class="form-item">
          <el-form-item label="内容来源">
            <el-select v-model="formData.contentSourceFilter" placeholder="选择内容来源" clearable>
              <el-option label="全部来源" :value="null"></el-option>
              <el-option label="文件内容" value="files_v2"></el-option>
              <el-option label="文本内容" value="text"></el-option>
            </el-select>
          </el-form-item>
        </div>
        
        <div class="form-item" style="flex: 0.5;">
          <el-form-item label="分组显示">
            <el-switch
              v-model="formData.enableGrouping"
              active-text="按关键词分组"
              inactive-text="传统列表"
              :active-value="true"
              :inactive-value="false"
              style="--el-switch-on-color: #667eea; --el-switch-off-color: #dcdfe6"
              @change="handleGroupingChange">
            </el-switch>
            <div v-if="formData.enableGrouping && isGroupedView" class="grouping-tip">
              <i class="el-icon-info"></i> 分组模式将自动展示全部数据
            </div>
          </el-form-item>
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-item">
          <el-form-item label="排序字段">
            <el-select v-model="formData.orderBy" placeholder="选择排序方式">
              <el-option label="发布时间 (降序)" value="public_time DESC"></el-option>
              <el-option label="发布时间 (升序)" value="public_time ASC"></el-option>
              <el-option label="相关性评分 (降序)" value="_score DESC"></el-option>
              <el-option label="相关性评分 (升序)" value="_score ASC"></el-option>
            </el-select>
          </el-form-item>
        </div>

        <div class="form-item">
          <el-form-item label="每页显示">
            <el-select v-model="formData.pageSize" placeholder="选择每页显示数量" @change="handlePageSizeChange">
              <el-option label="10条" :value="10"></el-option>
              <el-option label="25条" :value="25"></el-option>
              <el-option label="50条" :value="50"></el-option>
              <el-option label="100条" :value="100"></el-option>
              <el-option label="全部数据" :value="'all'"></el-option>
            </el-select>
          </el-form-item>
        </div>
        
        <div class="form-item">
          <el-form-item label="ES索引">
            <el-input
              v-model="formData.tableName"
              placeholder="Elasticsearch索引名称"
              readonly>
            </el-input>
          </el-form-item>
        </div>
        
        <div class="form-item" style="flex: 0.3;">
          <el-button 
            type="primary" 
            @click="handleSubmit" 
            :loading="loading"
            size="large"
            class="submit-button">
            <el-icon><Search /></el-icon>
            {{ loading ? '查询中...' : '执行查询' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, defineEmits, defineProps } from 'vue';

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'page-size-change', 'grouping-change'])

const isGroupedView = computed(() => {
  return props.formData.enableGrouping && props.formData.searchKeyword.includes(',');
})

const handleSubmit = () => {
  emit('submit')
}

const handlePageSizeChange = () => {
  emit('page-size-change')
}

const handleGroupingChange = (enabled) => {
  emit('grouping-change', enabled)
}
</script>

<style scoped>
.query-form-wrapper {
  margin-bottom: 30px;
}

.form-title {
  margin-bottom: 20px;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.grouping-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.submit-button {
  width: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
}
</style>