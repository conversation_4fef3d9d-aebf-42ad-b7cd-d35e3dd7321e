// 主应用程序文件
const { createApp } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

// 创建Vue应用
const app = createApp({
    data() {
        return createAppData();
    },
    
    computed: {
        ...createAppComputed()
    },
    
    methods: {
        ...createAppMethods(),
        ...createDialogMethods(),
        
        // 格式化日期
        formatDate: Utils.formatDate,

        // 格式化翻译时间
        formatTranslatedTime: Utils.formatTranslatedTime,
        
        // 获取主要内容
        getMainContent: Utils.getMainContent,
        
        // 判断是否为重要字段
        isImportantField: Utils.isImportantField,
        
        // 判断是否为长内容
        isLongContent: Utils.isLongContent,
        
        // 获取来源标签类型
        getSourceTagType: Utils.getSourceTagType,
        
        // 获取来源标签
        getSourceLabel: Utils.getSourceLabel,
        
        // 获取内容来源标签
        getContentSourceLabel: Utils.getContentSourceLabel
    },
    
    mounted() {
        ElMessage.info('欢迎使用数据库查询系统！请设置查询参数后点击执行查询。');
    }
});

// 使用Element Plus并挂载应用
app.use(ElementPlus).mount('#app');