// Vue应用方法定义
const createAppMethods = () => {
    return {
        // 执行查询
        async executeQuery() {
            // 验证输入
            if (!this.queryParams.searchKeyword.trim() && !this.queryParams.idSearch.trim()) {
                ElMessage.warning('请输入搜索关键词或ID');
                return;
            }

            this.loading = true;
            this.hasSearched = true;

            try {
                // 构建查询参数
                const queryParams = {
                    searchKeyword: this.queryParams.searchKeyword,
                    idSearch: this.queryParams.idSearch,
                    searchFields: this.queryParams.searchFields,
                    orderBy: this.queryParams.orderBy,
                    tableName: this.queryParams.tableName,
                    enableGrouping: this.queryParams.enableGrouping,
                    contentSourceFilter: this.queryParams.contentSourceFilter,
                    pageSize: this.queryParams.pageSize
                };

                // 根据分页类型添加相应参数
                if (this.queryParams.useCursorPagination && this.queryParams.cursor) {
                    // 使用游标分页
                    queryParams.cursor = this.queryParams.cursor;
                } else {
                    // 使用传统分页
                    queryParams.page = this.queryParams.page;
                }

                const response = await ApiService.query(queryParams);

                if (response.success) {
                    if (response.meta.grouped) {
                        // 分组数据
                        this.groupedResults = response.data || {};
                        this.totalRecords = response.meta.total_records || 0;
                        this.queryResults = [];

                        // 初始化分组展开状态
                        this.initializeExpandedStates();
                    } else {
                        // 传统列表数据
                        this.queryResults = response.data;
                        this.groupedResults = {};
                        this.totalRecords = response.meta.total;

                        // 更新分页信息
                        if (response.pagination) {
                            this.pagination = {
                                ...this.pagination,
                                ...response.pagination,
                                is_cursor_pagination: response.meta.is_cursor_pagination || false
                            };
                            
                            // 更新查询参数
                            if (!this.queryParams.useCursorPagination) {
                                this.queryParams.page = this.pagination.current_page;
                                
                                // 只有在非全部数据模式下才更新pageSize，避免被分页控件覆盖
                                if (this.pagination.page_size !== 'all') {
                                    this.queryParams.pageSize = this.pagination.page_size;
                                }
                            }
                        }
                    }

                    this.executionTime = response.meta.execution_time;
                    ElMessage.success(response.message);
                } else {
                    ElMessage.error(response.message);
                    this.queryResults = [];
                    this.groupedResults = {};
                }

            } catch (error) {
                console.error('查询失败:', error);

                // 重置状态
                this.queryResults = [];
                this.groupedResults = {};
                this.totalRecords = 0;
                this.executionTime = 0;

                // 如果API调用失败，使用模拟数据作为后备
                if (error.code === 'ERR_NETWORK') {
                    ElMessage.warning('无法连接到服务器，使用模拟数据展示');
                    this.queryResults = this.mockData.filter(item =>
                        this.queryParams.searchFields.some(field =>
                            item[field] && item[field].includes(this.queryParams.searchKeyword.split(',')[0])
                        )
                    );
                    this.executionTime = 150; // 模拟执行时间
                    this.totalRecords = this.queryResults.length;
                } else {
                    ElMessage.error('查询失败，请检查网络连接或参数设置');
                    this.queryResults = [];
                    this.groupedResults = {};
                }
            } finally {
                this.loading = false;
            }
        },

        // 查看记录详情
        async viewRecord(row) {
            try {
                const response = await ApiService.getRecord(row.id, this.queryParams.tableName);

                if (response.success) {
                    this.currentRecord = response.data;
                    this.detailDialogVisible = true;

                    // 确保对话框正确居中显示
                    this.$nextTick(() => {
                        this.ensureDialogCentered();
                    });
                } else {
                    ElMessage.error('获取记录详情失败');
                }
            } catch (error) {
                console.error('获取记录详情失败:', error);
                // 如果API调用失败，使用当前行数据
                this.currentRecord = row;
                this.detailDialogVisible = true;

                // 确保对话框正确居中显示
                this.$nextTick(() => {
                    this.ensureDialogCentered();
                });
            }
        },

        // 确保对话框居中显示
        ensureDialogCentered() {
            // 等待DOM更新完成，使用多次检查确保元素已渲染
            const checkAndCenter = (attempts = 0) => {
                const dialog = document.querySelector('.el-dialog');
                const overlay = document.querySelector('.el-overlay');
                const wrapper = document.querySelector('.el-dialog__wrapper');

                if (dialog && overlay) {
                    // 确保包装器样式
                    if (wrapper) {
                        wrapper.style.position = 'fixed';
                        wrapper.style.top = '0';
                        wrapper.style.left = '0';
                        wrapper.style.width = '100%';
                        wrapper.style.height = '100%';
                        wrapper.style.zIndex = '2000';
                        wrapper.style.display = 'flex';
                        wrapper.style.alignItems = 'center';
                        wrapper.style.justifyContent = 'center';
                    }

                    // 确保遮罩层覆盖整个视口
                    overlay.style.position = 'fixed';
                    overlay.style.top = '0';
                    overlay.style.left = '0';
                    overlay.style.width = '100vw';
                    overlay.style.height = '100vh';
                    overlay.style.zIndex = '2000';

                    // 确保对话框居中
                    dialog.style.position = 'relative';
                    dialog.style.top = 'auto';
                    dialog.style.left = 'auto';
                    dialog.style.transform = 'none';
                    dialog.style.margin = '0';
                    dialog.style.maxHeight = '90vh';

                    // 响应式宽度设置
                    const viewportWidth = window.innerWidth;
                    if (viewportWidth <= 480) {
                        dialog.style.width = '98%';
                        dialog.style.maxWidth = '98%';
                        dialog.style.maxHeight = '95vh';
                    } else if (viewportWidth <= 768) {
                        dialog.style.width = '95%';
                        dialog.style.maxWidth = '95%';
                    } else {
                        dialog.style.width = '90%';
                        dialog.style.maxWidth = '90%';
                    }

                    // 确保对话框内容可滚动
                    const dialogBody = dialog.querySelector('.el-dialog__body');
                    if (dialogBody) {
                        const maxHeight = viewportWidth <= 480 ? 'calc(95vh - 100px)' : 'calc(90vh - 120px)';
                        dialogBody.style.maxHeight = maxHeight;
                        dialogBody.style.overflowY = 'auto';
                    }

                    console.log('对话框居中设置完成');
                } else if (attempts < 10) {
                    // 如果元素还没有渲染，继续尝试
                    setTimeout(() => checkAndCenter(attempts + 1), 100);
                }
            };

            // 立即执行一次，然后延迟执行确保样式生效
            checkAndCenter();
            setTimeout(() => checkAndCenter(), 50);
        },

        // 打开URL
        openUrl(url) {
            if (url) {
                window.open(url, '_blank');
            }
        },

        // 分组相关方法
        toggleGroup(groupKey) {
            const newState = { ...this.groupExpandedState };
            newState[groupKey] = !newState[groupKey];
            this.groupExpandedState = newState;
        },

        toggleSource(groupKey, sourceKey) {
            try {
                const newState = { ...this.sourceExpandedState };
                if (!newState[groupKey]) {
                    newState[groupKey] = {};
                }
                newState[groupKey] = { ...newState[groupKey] };
                newState[groupKey][sourceKey] = !newState[groupKey][sourceKey];

                this.sourceExpandedState = newState;
            } catch (error) {
                console.error('切换来源展开状态时出错:', error);
            }
        },

        getSourceExpandedState(groupKey, sourceKey) {
            try {
                return this.sourceExpandedState &&
                       this.sourceExpandedState[groupKey] &&
                       this.sourceExpandedState[groupKey][sourceKey];
            } catch (error) {
                console.warn('获取来源展开状态时出错:', error);
                return true; // 默认展开
            }
        },

        // 初始化展开状态
        initializeExpandedStates() {
            const newGroupExpandedState = {};
            const newSourceExpandedState = {};

            Object.keys(this.groupedResults).forEach(groupKey => {
                newGroupExpandedState[groupKey] = true;

                const groupData = this.groupedResults[groupKey];
                if (groupData && groupData.sources) {
                    newSourceExpandedState[groupKey] = {};
                    Object.keys(groupData.sources).forEach(sourceKey => {
                        newSourceExpandedState[groupKey][sourceKey] = true;
                    });
                }
            });

            this.groupExpandedState = newGroupExpandedState;
            this.sourceExpandedState = newSourceExpandedState;

            this.$nextTick(() => {
                this.$forceUpdate();
            });
        },

        // 分页相关方法
        handleCurrentChange(page) {
            this.queryParams.page = page;
            this.executeQuery();
        },

        handleSizeChange(size) {
            if (this.queryParams.pageSize === 'all') {
                return;
            }

            this.queryParams.pageSize = size;
            this.queryParams.page = 1;
            this.executeQuery();
        },

        handlePageSizeChange() {
            this.queryParams.page = 1;

            if (this.queryParams.pageSize === 'all') {
                ElMessage.info('已切换到全部数据模式，将显示所有搜索结果（最多5000条）');
            }

            this.executeQuery();
        },

        handleGroupingChange(enabled) {
            if (enabled && this.queryParams.searchKeyword.includes(',')) {
                ElMessage.info('分组模式已启用，将自动展示全部数据以完整显示分组结果');
            }
        },

        // 游标分页相关方法
        nextPage() {
            if (this.pagination.has_next && this.pagination.next_cursor) {
                this.queryParams.cursor = this.pagination.next_cursor;
                this.queryParams.useCursorPagination = true;
                this.executeQuery();
            }
        },

        resetToFirstPage() {
            this.queryParams.cursor = null;
            this.queryParams.page = 1;
            this.queryParams.useCursorPagination = false;
            this.executeQuery();
        }
    };
};