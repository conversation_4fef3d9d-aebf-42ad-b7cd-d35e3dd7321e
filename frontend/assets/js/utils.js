// 应用配置已移至 config.js 文件
// APP_CONFIG 现在通过配置管理器动态获取

// API 请求工具类
class ApiService {
    static async query(params) {
        const response = await axios.post(`${APP_CONFIG.API_BASE_URL}/query`, params);
        return response.data;
    }
    
    static async getRecord(id, tableName) {
        const response = await axios.get(`${APP_CONFIG.API_BASE_URL}/record/${id}?table=${tableName}`);
        return response.data;
    }
}

// 工具函数
const Utils = {
    formatExecutionTime(time) {
        return time > 0 ? `${time}ms` : '-';
    },
    
    formatDate(dateString) {
        if (!dateString) return '-';
        return dateString;
    },

    formatTranslatedTime(translatedTime) {
        if (!translatedTime) return '-';
        try {
            // 如果是ISO格式的时间字符串，格式化为可读格式
            if (typeof translatedTime === 'string' && translatedTime.includes('T')) {
                const date = new Date(translatedTime);
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            }
            return translatedTime;
        } catch (e) {
            return translatedTime;
        }
    },
    
    isLongContent(value) {
        return typeof value === 'string' && value.length > APP_CONFIG.MAX_CONTENT_LENGTH;
    },
    
    isImportantField(key) {
        const importantFields = [
            'id', 'title', 'content', 'text', 'dynamic_content', 
            'dynamic_content_highlighted', 'keyword', 'public_time', 
            'url', 'content_source', '_score', 'isAddToQueue'
        ];
        return importantFields.includes(key);
    },
    

    
    getSourceTagType(source) {
        const types = {
            'files_v2': 'primary',
            'text': 'success',
            'unknown': 'info'
        };
        return types[source] || 'info';
    },
    
    getSourceLabel(source) {
        const labels = {
            'files_v2': '文件内容',
            'text': '文本内容',
            'unknown': '未知来源'
        };
        return labels[source] || source;
    },
    
    getContentSourceLabel(value) {
        const labels = {
            'files_v2': '文件内容',
            'text': '文本内容'
        };
        return labels[value] || value;
    }
};