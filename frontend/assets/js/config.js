// 前端配置管理
class ConfigManager {
    constructor() {
        this.config = null;
        this.isLoaded = false;
    }

    // 加载配置
    async loadConfig() {
        if (this.isLoaded) {
            return this.config;
        }

        try {
            // 从后端API获取配置
            const response = await axios.get('/api/config');
            if (response.data.success) {
                this.config = response.data.data;
                this.isLoaded = true;
                console.log('配置加载成功:', this.config);
                return this.config;
            } else {
                throw new Error(response.data.message || '配置加载失败');
            }
        } catch (error) {
            console.error('配置加载失败，使用默认配置:', error);
            // 使用默认配置作为后备
            this.config = this.getDefaultConfig();
            this.isLoaded = true;
            return this.config;
        }
    }

    // 获取默认配置（后备方案）
    getDefaultConfig() {
        return {
            api_base_url: 'http://127.0.0.1:8081/api',
            default_table_name: 'dc_tradecontrol',
            default_page_size: 50,
            max_content_length: 200,
            default_search_fields: ['files_v2.file_v2_original', 'text'],
            default_order_by: 'public_time DESC'
        };
    }

    // 获取配置项
    get(key, defaultValue = null) {
        if (!this.isLoaded || !this.config) {
            console.warn('配置尚未加载，返回默认值');
            const defaultConfig = this.getDefaultConfig();
            return defaultConfig[key] || defaultValue;
        }
        return this.config[key] || defaultValue;
    }

    // 获取API基础URL
    getApiBaseUrl() {
        return this.get('api_base_url', 'http://127.0.0.1:8081/api');
    }

    // 获取默认表名
    getDefaultTableName() {
        return this.get('default_table_name', 'dc_tradecontrol');
    }

    // 获取默认页面大小
    getDefaultPageSize() {
        return this.get('default_page_size', 50);
    }

    // 获取最大内容长度
    getMaxContentLength() {
        return this.get('max_content_length', 200);
    }

    // 获取默认搜索字段
    getDefaultSearchFields() {
        return this.get('default_search_fields', ['files_v2.file_v2_original', 'text']);
    }

    // 获取默认排序
    getDefaultOrderBy() {
        return this.get('default_order_by', 'public_time DESC');
    }
}

// 创建全局配置管理器实例
const configManager = new ConfigManager();

// 兼容性：保持原有的APP_CONFIG对象，但从配置管理器获取值
const APP_CONFIG = {
    get API_BASE_URL() {
        return configManager.getApiBaseUrl();
    },
    get DEFAULT_TABLE_NAME() {
        return configManager.getDefaultTableName();
    },
    get DEFAULT_PAGE_SIZE() {
        return configManager.getDefaultPageSize();
    },
    get MAX_CONTENT_LENGTH() {
        return configManager.getMaxContentLength();
    }
};

// 导出配置管理器
window.configManager = configManager;
window.APP_CONFIG = APP_CONFIG;
