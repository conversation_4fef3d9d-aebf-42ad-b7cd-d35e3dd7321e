/* 主要样式文件 */
body {
    margin: 0;
    padding: 20px;
    background: #f5f7fa;
    min-height: 100vh;
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
    color: #333;
}

.container {
    max-width: 95%;
    width: 100%;
    margin: 0 auto;
    background: #fff;
    padding: 30px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.header h1 {
    color: #2c3e50;
    font-size: 2.2em;
    margin-bottom: 10px;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.header p {
    color: #7f8c8d;
    font-size: 1.1em;
    font-weight: 400;
}

.results-section {
    margin-top: 25px;
}