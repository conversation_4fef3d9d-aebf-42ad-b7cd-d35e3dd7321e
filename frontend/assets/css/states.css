/* 加载和空状态样式 */
.loading-container {
    text-align: center;
    padding: 60px 40px;
    background: #fff;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.loading-container p {
    margin-top: 20px;
    color: #667eea;
    font-size: 1.1em;
    font-weight: 500;
}

.no-data {
    text-align: center;
    padding: 60px 40px;
    background: #fff;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.no-data h3 {
    color: #2c3e50;
    margin: 20px 0 15px 0;
}

.no-data p {
    color: #7f8c8d;
    font-size: 1em;
}