/* 表格相关样式 - 简洁设计 */
.table-container {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e9ecef;
    margin-bottom: 25px;
}

.el-table {
    border-radius: 8px;
    width: 100% !important;
    overflow: hidden;
    border: none !important;
    background: transparent;
}

.el-table .el-table__body-wrapper {
    overflow-x: auto;
}

.el-table .el-table__body-wrapper::-webkit-scrollbar {
    height: 6px;
}

.el-table .el-table__body-wrapper::-webkit-scrollbar-track {
    background: #f1f3f4;
}

.el-table .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background: #c1c9d2;
    border-radius: 3px;
}

.el-table th {
    background: #f8f9fa !important;
    color: #2c3e50 !important;
    font-weight: 600;
    border: none !important;
    font-size: 0.9em;
    padding: 16px 12px !important;
}

.el-table td {
    border-bottom: 1px solid #e9ecef;
    padding: 14px 12px;
    background: #fff;
}

.el-table tr:nth-child(even) td {
    background: #f8f9fa;
}

/* 内容预览样式 */
.content-preview {
    max-width: 550px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9em;
    line-height: 1.4;
    color: #2c3e50;
}

/* 表格单元格内容样式 */
.title-cell {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.95em;
    line-height: 1.4;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.title-cell:hover {
    color: #667eea;
    cursor: pointer;
}

/* 日期时间样式 */
.date-time-cell {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85em;
    color: #5a6c7d;
}

.date-time-cell .el-icon {
    color: #667eea;
    font-size: 1em;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
}

.el-button {
    border-radius: 6px !important;
    font-weight: 500 !important;
    border: 1px solid #dcdfe6 !important;
    box-shadow: none !important;
    font-size: 0.85em;
    padding: 6px 12px !important;
}

.el-button:hover {
    border-color: #667eea !important;
    color: #667eea !important;
    background: #f8f9fa !important;
}

.el-button--primary {
    background: #667eea !important;
    border-color: #667eea !important;
    color: white !important;
}

.el-button--success {
    background: #4caf50 !important;
    border-color: #4caf50 !important;
    color: white !important;
}

/* 表格加载状态 */
.el-table__empty-block {
    background: #fff !important;
    padding: 30px !important;
}

.el-table__empty-text {
    color: #6c757d !important;
    font-size: 1em !important;
}

/* 响应式优化 */
@media (max-width: 1200px) {
    .content-preview {
        max-width: 400px;
    }

    .title-cell {
        max-width: 200px;
    }
}

@media (max-width: 768px) {
    .el-table td {
        padding: 10px 8px;
        font-size: 0.85em;
    }

    .el-table th {
        padding: 12px 8px !important;
        font-size: 0.8em;
    }

    .content-preview {
        max-width: 250px;
        padding: 6px 10px;
        font-size: 0.8em;
    }

    .title-cell {
        max-width: 150px;
        font-size: 0.85em;
    }

    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .date-time-cell {
        font-size: 0.75em;
    }
}

@media (max-width: 480px) {
    .el-table td {
        padding: 8px 4px;
    }

    .el-table th {
        padding: 10px 4px !important;
    }

    .content-preview {
        max-width: 180px;
        padding: 5px 8px;
    }

    .title-cell {
        max-width: 120px;
    }

    .action-buttons .el-button {
        padding: 4px 8px !important;
        font-size: 0.75em !important;
    }
}