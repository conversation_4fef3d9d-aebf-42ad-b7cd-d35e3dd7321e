/* 分组样式 */
.group-container {
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.group-header {
    background: #f8f9fa;
    color: #2c3e50;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.group-header:hover {
    background: #e9ecef;
}

.group-title {
    font-size: 1em;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.group-count {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.group-content {
    background: white;
}

.keyword-combination {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.combination-tag {
    background: #667eea;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.group-toggle-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 1em;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.group-toggle-btn.collapsed {
    transform: rotate(-90deg);
}

.view-mode-switch {
    margin-bottom: 20px;
    text-align: center;
}

.mode-switch-btn {
    margin: 0 10px;
    padding: 8px 20px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-switch-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.group-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.group-stat-item {
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #667eea;
    flex: 1;
    min-width: 120px;
}

.group-stat-number {
    font-size: 1.3em;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.group-stat-label {
    font-size: 0.85em;
    color: #7f8c8d;
}