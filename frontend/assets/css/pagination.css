/* 分页容器样式 */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    margin: 20px 0;
    border-top: 1px solid #e9ecef;
}

/* 游标分页样式 */
.cursor-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 0;
}

.cursor-pagination .el-button {
    border-radius: 6px !important;
    font-weight: 500 !important;
    border: 1px solid #dcdfe6 !important;
    box-shadow: none !important;
    font-size: 0.9em;
    padding: 8px 16px !important;
}

.cursor-pagination .el-button:hover {
    border-color: #667eea !important;
    color: #667eea !important;
    background: #f8f9fa !important;
}

.cursor-pagination .el-button:disabled {
    opacity: 0.6;
    cursor: not-allowed !important;
}

.cursor-pagination .el-button--primary {
    background: #667eea !important;
    border-color: #667eea !important;
    color: white !important;
}

.pagination-info {
    font-size: 0.9em;
    color: #6c757d;
    font-weight: 500;
    padding: 6px 12px;
    background: #f8f9fa;
    border-radius: 16px;
    border: 1px solid #e9ecef;
}

/* Element Plus 分页组件样式优化 */
.el-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    padding: 10px 0;
}

.el-pagination.is-background .el-pager li {
    background-color: #f4f4f5;
    color: #606266;
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    min-width: 32px;
    height: 32px;
    line-height: 30px;
    font-size: 0.9em;
    margin: 0 2px;
}

.el-pagination.is-background .el-pager li.active {
    background-color: #667eea;
    color: #fff;
    border-color: #667eea;
}

.el-pagination.is-background .el-pager li:hover {
    color: #667eea;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev {
    background-color: #f4f4f5;
    color: #606266;
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    min-width: 32px;
    height: 32px;
    line-height: 30px;
    font-size: 0.9em;
    margin: 0 2px;
}

.el-pagination.is-background .btn-next:hover,
.el-pagination.is-background .btn-prev:hover {
    color: #667eea;
    border-color: #667eea;
}

.el-pagination.is-background .btn-next:disabled,
.el-pagination.is-background .btn-prev:disabled {
    color: #c0c4cc;
    background-color: #f4f4f5;
    border-color: #e4e7ed;
}

.el-pagination .el-pagination__total {
    color: #6c757d;
    font-size: 0.9em;
    margin-right: 10px;
}

.el-pagination .el-pagination__sizes {
    margin: 0 10px;
}

.el-pagination .el-pagination__sizes .el-select {
    width: 100px;
}

.el-pagination .el-pagination__jump {
    margin-left: 15px;
    color: #6c757d;
    font-size: 0.9em;
}

.el-pagination .el-pagination__jump .el-input {
    width: 60px;
    margin: 0 5px;
}

/* 全部数据提示样式 */
.pagination-container .data-info {
    text-align: center;
    color: #666;
    font-size: 14px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    width: 100%;
}

.pagination-container .data-info .el-icon {
    margin-right: 5px;
}

.pagination-container .data-info .warning {
    color: #e6a23c;
    margin-left: 10px;
}