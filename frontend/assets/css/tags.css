/* 标签和徽章样式 - 现代化设计 */
.keyword-tag {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    padding: 6px 14px;
    border-radius: 25px;
    font-size: 0.85em;
    margin: 3px;
    display: inline-block;
    border: 1px solid rgba(21, 101, 192, 0.2);
    font-weight: 600;
    letter-spacing: 0.03em;
    box-shadow: 0 2px 8px rgba(21, 101, 192, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
}

.keyword-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(21, 101, 192, 0.25);
    background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
}

.date-badge {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    padding: 6px 14px;
    border-radius: 25px;
    font-size: 0.85em;
    border: 1px solid rgba(73, 80, 87, 0.15);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(73, 80, 87, 0.1);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.date-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(73, 80, 87, 0.15);
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.url-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.url-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #5a6fd8;
    transform: translateX(2px);
}

/* Element Plus 标签样式优化 */
.el-tag {
    border-radius: 16px !important;
    font-weight: 500 !important;
    font-size: 0.8em !important;
    padding: 4px 12px !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.2s ease !important;
    background: rgba(255, 255, 255, 0.8) !important;
}

.el-tag:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
}

.el-tag--primary {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
}

.el-tag--success {
    background: rgba(76, 175, 80, 0.1) !important;
    color: #4caf50 !important;
}

.el-tag--info {
    background: rgba(108, 117, 125, 0.1) !important;
    color: #6c757d !important;
}

.el-tag--warning {
    background: rgba(255, 193, 7, 0.1) !important;
    color: #ffc107 !important;
}

.el-tag--danger {
    background: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
}

/* 内容来源标签特殊样式 */
.content-source-files {
    background: rgba(232, 245, 232, 0.7) !important;
    color: #2e7d32 !important;
    border: 1px solid rgba(46, 125, 50, 0.1) !important;
    box-shadow: 0 1px 3px rgba(46, 125, 50, 0.05) !important;
}

.content-source-text {
    background: rgba(255, 243, 224, 0.7) !important;
    color: #ef6c00 !important;
    border: 1px solid rgba(239, 108, 0, 0.1) !important;
    box-shadow: 0 1px 3px rgba(239, 108, 0, 0.05) !important;
}

/* 翻译时间特殊样式 */
.translated-time-badge {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    padding: 6px 14px;
    border-radius: 25px;
    font-size: 0.85em;
    border: 1px solid rgba(73, 80, 87, 0.15);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(73, 80, 87, 0.1);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.translated-time-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(73, 80, 87, 0.15);
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}