# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
pip-log.txt
pip-delete-this-directory.txt

# Virtual Environment
.venv/
venv/
ENV/
env/
.conda/
conda-meta/

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Django
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Node.js / npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm/
.eslintcache
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE - Keep .vscode for now as it contains useful project configs
.idea/
*.swp
*.swo
.project
.classpath
.settings/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Logs and monitoring
logs/
*.log
*.log.*
es_monitor.log
app.log
error.log
performance.log
elasticsearch.log
access.log
debug.log
info.log
warn.log
warning.log
critical.log
fatal.log

# PID files (process ID files)
*.pid

# AI Tools
.claude/
.codebuddy/
.cursor/

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*~
database-doo

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration backups
*.conf.bak
*.config.bak

# Application specific
frontend/index_backup.html
status_report.json

# Security
*.key
*.pem
*.p12
*.pfx
secrets.json
credentials.json

# Performance monitoring
*.prof
*.trace