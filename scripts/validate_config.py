#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证脚本
Configuration Validation Script
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def check_env_file():
    """检查.env文件是否存在"""
    print("🔍 检查环境变量文件...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("  ❌ .env文件不存在")
        print("  💡 请复制.env.example为.env并配置相应参数")
        return False
    
    print("  ✅ .env文件存在")
    return True

def validate_required_vars():
    """验证必需的环境变量"""
    print("\n🔧 验证必需的环境变量...")
    
    # 加载环境变量
    load_dotenv()
    
    required_vars = {
        # ES配置
        'ES_HOST': '必需的Elasticsearch主机地址',
        'ES_PORT': '必需的Elasticsearch端口',
        'ES_USERNAME': '必需的Elasticsearch用户名',
        'ES_PASSWORD': '必需的Elasticsearch密码',
        'ES_INDEX': '必需的Elasticsearch索引名',
        
        # 应用配置
        'APP_HOST': '必需的应用主机地址',
        'APP_PORT': '必需的应用端口',
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            print(f"  ✅ {var}: {value}")
        else:
            print(f"  ❌ {var}: 未设置 ({description})")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  缺少必需的环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✅ 所有必需的环境变量已设置")
    return True

def validate_optional_vars():
    """验证可选的环境变量"""
    print("\n🔧 验证可选的环境变量...")
    
    optional_vars = {
        # 缓存配置
        'CACHE_TTL': '300',
        'CACHE_SIZE': '200',
        
        # 日志配置
        'LOG_LEVEL': 'INFO',
        'LOG_DIR': 'logs',
        
        # 前端配置
        'FRONTEND_API_BASE_URL': 'http://127.0.0.1:8081/api',
        'FRONTEND_DEFAULT_TABLE_NAME': 'dc_tradecontrol',
        'FRONTEND_DEFAULT_PAGE_SIZE': '50',
        'FRONTEND_MAX_CONTENT_LENGTH': '200',
        
        # ES服务默认配置
        'ES_DEFAULT_SOURCE_FIELDS': 'title,text,files_v2,public_time,createdAt,source,url,translated_time,_id',
        'ES_DEFAULT_SEARCH_FIELDS': 'files_v2.file_v2_original,text',
        'ES_DEFAULT_ORDER_BY': 'public_time DESC',
        
        # 系统路径配置
        'FRONTEND_DIR': 'frontend',
        'LOGS_DIR': 'logs',
        'SCRIPTS_DIR': 'scripts',
    }
    
    for var, default_value in optional_vars.items():
        value = os.getenv(var, default_value)
        print(f"  ✅ {var}: {value}")
    
    print("✅ 可选环境变量检查完成")
    return True

def validate_paths():
    """验证路径配置"""
    print("\n📁 验证路径配置...")
    
    # 从环境变量获取路径
    frontend_dir = os.getenv('FRONTEND_DIR', 'frontend')
    logs_dir = os.getenv('LOGS_DIR', 'logs')
    scripts_dir = os.getenv('SCRIPTS_DIR', 'scripts')
    
    paths_to_check = [
        (frontend_dir, '前端目录'),
        (scripts_dir, '脚本目录'),
    ]
    
    # 检查目录是否存在
    for path, description in paths_to_check:
        if Path(path).exists():
            print(f"  ✅ {description}: {path}")
        else:
            print(f"  ❌ {description}不存在: {path}")
            return False
    
    # 检查日志目录，不存在则创建
    logs_path = Path(logs_dir)
    if not logs_path.exists():
        try:
            logs_path.mkdir(parents=True, exist_ok=True)
            print(f"  ✅ 创建日志目录: {logs_dir}")
        except Exception as e:
            print(f"  ❌ 创建日志目录失败: {e}")
            return False
    else:
        print(f"  ✅ 日志目录: {logs_dir}")
    
    print("✅ 路径配置验证通过")
    return True

def validate_numeric_configs():
    """验证数值配置"""
    print("\n🔢 验证数值配置...")
    
    numeric_configs = {
        'ES_PORT': (1, 65535, '端口号必须在1-65535之间'),
        'APP_PORT': (1, 65535, '端口号必须在1-65535之间'),
        'CACHE_TTL': (1, None, '缓存TTL必须大于0'),
        'CACHE_SIZE': (1, None, '缓存大小必须大于0'),
        'FRONTEND_DEFAULT_PAGE_SIZE': (1, 1000, '页面大小必须在1-1000之间'),
        'FRONTEND_MAX_CONTENT_LENGTH': (1, None, '最大内容长度必须大于0'),
    }
    
    for var, (min_val, max_val, error_msg) in numeric_configs.items():
        value_str = os.getenv(var)
        if value_str:
            try:
                value = int(value_str)
                if min_val is not None and value < min_val:
                    print(f"  ❌ {var}: {value} - {error_msg}")
                    return False
                if max_val is not None and value > max_val:
                    print(f"  ❌ {var}: {value} - {error_msg}")
                    return False
                print(f"  ✅ {var}: {value}")
            except ValueError:
                print(f"  ❌ {var}: {value_str} - 必须是有效的数字")
                return False
    
    print("✅ 数值配置验证通过")
    return True

def main():
    """主函数"""
    print("🔍 开始配置验证...")
    print("=" * 50)
    
    # 确保在正确的目录中运行
    script_dir = Path(__file__).parent.parent
    os.chdir(script_dir)
    
    checks = [
        check_env_file,
        validate_required_vars,
        validate_optional_vars,
        validate_paths,
        validate_numeric_configs,
    ]
    
    all_passed = True
    for check in checks:
        if not check():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有配置验证通过！")
        print("🚀 系统配置正确，可以启动应用")
        return 0
    else:
        print("❌ 配置验证失败！")
        print("🔧 请修复上述问题后重新运行验证")
        return 1

if __name__ == '__main__':
    sys.exit(main())
