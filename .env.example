# Elasticsearch配置 - 线上环境
ES_HOST=es-cn-n8m2z2yo900054ak5.public.elasticsearch.aliyuncs.com
ES_PORT=9200
ES_USERNAME=elastic
ES_PASSWORD=elastic_n8m2z2yo
ES_INDEX=dc_tradecontrol
ES_TIMEOUT=15
ES_MAX_RETRIES=3

# 应用配置
APP_HOST=127.0.0.1
APP_PORT=8081
DEBUG=True

# 缓存配置
CACHE_TTL=300
CACHE_SIZE=200

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
LOG_ERROR_BACKUP_COUNT=5
LOG_PERF_BACKUP_COUNT=3
LOG_ACCESS_MAX_BYTES=5242880
LOG_ACCESS_BACKUP_COUNT=3

# 性能监控配置
MONITOR_MAX_HISTORY=1000
MONITOR_REQUEST_HISTORY=100
MONITOR_CPU_WARNING_THRESHOLD=80
MONITOR_MEMORY_WARNING_THRESHOLD=85
MONITOR_ERROR_RATE_THRESHOLD=5
MONITOR_RESPONSE_TIME_WARNING=2000

# ES服务配置
ES_REQUEST_TIMEOUT=15
ES_SCROLL_TIMEOUT=5m
ES_SEARCH_TIMEOUT=10s

# 健康检查配置
HEALTH_CHECK_TIMEOUT=5

# 前端配置
FRONTEND_API_BASE_URL=http://127.0.0.1:8081/api
FRONTEND_DEFAULT_TABLE_NAME=dc_tradecontrol
FRONTEND_DEFAULT_PAGE_SIZE=50
FRONTEND_MAX_CONTENT_LENGTH=200

# ES服务默认配置
ES_DEFAULT_SOURCE_FIELDS=title,text,files_v2,public_time,createdAt,source,url,translated_time,_id
ES_DEFAULT_SEARCH_FIELDS=files_v2.file_v2_original,text
ES_DEFAULT_ORDER_BY="public_time DESC"

# 系统路径配置
FRONTEND_DIR=frontend
LOGS_DIR=logs
SCRIPTS_DIR=scripts

# 健康检查配置
HEALTH_CHECK_TIMEOUT=5