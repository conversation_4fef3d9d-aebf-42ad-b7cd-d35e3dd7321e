#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端配置API模块
Frontend Configuration API Module
"""

import os
from flask import Blueprint, jsonify
from app.core import get_logger

# 创建配置蓝图
config_bp = Blueprint('config', __name__)
logger = get_logger(__name__)


@config_bp.route('/config', methods=['GET'])
def get_frontend_config():
    """
    获取前端配置
    Get frontend configuration
    """
    try:
        # 从环境变量读取前端配置
        config = {
            'api_base_url': os.getenv('FRONTEND_API_BASE_URL', 'http://127.0.0.1:8081/api'),
            'default_table_name': os.getenv('FRONTEND_DEFAULT_TABLE_NAME', 'dc_tradecontrol'),
            'default_page_size': int(os.getenv('FRONTEND_DEFAULT_PAGE_SIZE', '50')),
            'max_content_length': int(os.getenv('FRONTEND_MAX_CONTENT_LENGTH', '200')),
            'default_search_fields': os.getenv('ES_DEFAULT_SEARCH_FIELDS', 'files_v2.file_v2_original,text').split(','),
            'default_order_by': os.getenv('ES_DEFAULT_ORDER_BY', 'public_time DESC')
        }
        
        logger.info("前端配置获取成功")
        return jsonify({
            'success': True,
            'data': config,
            'message': '配置获取成功'
        })
        
    except Exception as e:
        logger.error(f"获取前端配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '配置获取失败'
        }), 500


@config_bp.route('/config/app-info', methods=['GET'])
def get_app_info():
    """
    获取应用信息
    Get application information
    """
    try:
        app_info = {
            'name': '数据库查询系统',
            'version': '2.0',
            'description': 'Elasticsearch查询API服务 (优化版)',
            'environment': 'development' if os.getenv('DEBUG', 'True').lower() == 'true' else 'production'
        }
        
        return jsonify({
            'success': True,
            'data': app_info,
            'message': '应用信息获取成功'
        })
        
    except Exception as e:
        logger.error(f"获取应用信息失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '应用信息获取失败'
        }), 500
