#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的Elasticsearch查询服务
"""

import os
import time
import json
import hashlib
import threading
import logging
import uuid
from datetime import datetime
from collections import OrderedDict
from typing import List, Dict, Any, Optional, Union
from elasticsearch import Elasticsearch
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()
from elasticsearch.exceptions import ConnectionError, RequestError, NotFoundError

# 导入查询模板和分析器
from app.services.query_templates import template_manager, QueryTemplateType
from app.services.query_analyzer import query_analyzer
from app.services.cursor_paginator import CursorPaginator


class ESConnectionManager:
    """ES连接管理器 - 单例模式"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.es_client = None
            self.config = None
            self.initialized = True
            self.logger = logging.getLogger(__name__)
    
    def initialize(self, config: Dict[str, Any]):
        """初始化ES客户端"""
        self.config = config
        self._create_client()
    
    def get_client(self) -> Optional[Elasticsearch]:
        """获取ES客户端"""
        if self.es_client is None or not self._health_check():
            self._create_client()
        return self.es_client
    
    def _create_client(self):
        """创建ES客户端"""
        try:
            # 使用URL中包含认证信息的方式，兼容阿里云ES
            es_url = f"http://{self.config['username']}:{self.config['password']}@{self.config['host']}:{self.config['port']}"

            # 从环境变量获取超时和重试配置
            request_timeout = int(os.getenv('ES_REQUEST_TIMEOUT', '15'))
            max_retries = int(os.getenv('ES_MAX_RETRIES', '3'))

            self.es_client = Elasticsearch(
                [es_url],
                request_timeout=request_timeout,
                max_retries=max_retries,
                retry_on_timeout=True,
                http_compress=True,
                verify_certs=False
            )
            
            # 测试连接 - 使用更兼容的方法
            try:
                # 尝试获取集群信息而不是使用ping
                cluster_info = self.es_client.info()
                self.logger.info(f"ES连接成功: {cluster_info.get('cluster_name', 'unknown')}")
            except Exception as ping_error:
                self.logger.error(f"ES连接失败: {ping_error}")
                self.es_client = None
                
        except Exception as e:
            self.logger.error(f"创建ES客户端失败: {e}")
            self.es_client = None
    
    def _health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.es_client:
                return False
            # 使用info()方法代替ping()
            self.es_client.info()
            return True
        except:
            return False


class ESCache:
    """ES查询缓存"""
    def __init__(self, max_size: int = None, ttl: int = None):
        # 从环境变量获取缓存配置
        self.max_size = max_size or int(os.getenv('CACHE_SIZE', '200'))
        self.ttl = ttl or int(os.getenv('CACHE_TTL', '300'))
        self.cache = OrderedDict()
        self.timestamps = {}
        self._lock = threading.Lock()
    
    def _generate_key(self, **kwargs) -> str:
        """生成缓存键"""
        key_data = json.dumps(kwargs, sort_keys=True, default=str)
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, **kwargs) -> Optional[Any]:
        """获取缓存"""
        key = self._generate_key(**kwargs)
        current_time = time.time()
        
        with self._lock:
            if key in self.cache:
                # 检查是否过期
                if current_time - self.timestamps[key] < self.ttl:
                    # 移到末尾（LRU）
                    self.cache.move_to_end(key)
                    return self.cache[key]
                else:
                    # 过期删除
                    del self.cache[key]
                    del self.timestamps[key]
        
        return None
    
    def set(self, value: Any, **kwargs):
        """设置缓存"""
        key = self._generate_key(**kwargs)
        current_time = time.time()
        
        with self._lock:
            # 如果已存在，更新
            if key in self.cache:
                self.cache[key] = value
                self.timestamps[key] = current_time
                self.cache.move_to_end(key)
            else:
                # 检查容量
                if len(self.cache) >= self.max_size:
                    # 删除最旧的
                    oldest_key = next(iter(self.cache))
                    del self.cache[oldest_key]
                    del self.timestamps[oldest_key]
                
                self.cache[key] = value
                self.timestamps[key] = current_time
    
    def clear_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        with self._lock:
            expired_keys = [
                key for key, timestamp in self.timestamps.items()
                if current_time - timestamp >= self.ttl
            ]
            
            for key in expired_keys:
                del self.cache[key]
                del self.timestamps[key]


class ESQueryBuilder:
    """ES查询构建器"""
    def __init__(self, index_name: str):
        self.index_name = index_name
        self.query_body = {
            "query": {"bool": {"must": [], "filter": [], "should": [], "must_not": []}},
            "sort": [],
            "size": 50,
            "from": 0,
            "_source": {"includes": []},
            "timeout": os.getenv('ES_SEARCH_TIMEOUT', '10s')
        }
    
    def add_keyword_search(self, keywords: List[str], fields: List[str], 
                          boost_map: Optional[Dict[str, float]] = None) -> 'ESQueryBuilder':
        """添加关键词搜索"""
        if not boost_map:
            boost_map = {"files_v2.file_v2_original": 2.0, "text": 1.0}
        
        # 为每个关键词创建跨字段搜索
        for keyword in keywords:
            should_clauses = []
            for field in fields:
                boost = boost_map.get(field, 1.0)
                # 使用更宽松的匹配策略
                should_clauses.append({
                    "match": {
                        field: {
                            "query": keyword, 
                            "boost": boost,
                            "operator": "or",  # 改为or，避免过度严格
                            "minimum_should_match": "75%"  # 75%的词匹配即可
                        }
                    }
                })
            
            self.query_body["query"]["bool"]["should"].append({
                "bool": {"should": should_clauses, "minimum_should_match": 1}
            })
        
        # 多关键词之间使用OR关系，避免过度过滤
        self.query_body["query"]["bool"]["minimum_should_match"] = 1
        return self
    
    def add_content_source_filter(self, source_type: str) -> 'ESQueryBuilder':
        """添加内容源过滤 - 修复数据丢失问题"""
        if source_type == 'files_v2':
            # files_v2过滤：只要有files_v2字段且不为空
            self.query_body["query"]["bool"]["filter"].append({
                "bool": {
                    "must": [
                        {"exists": {"field": "files_v2"}},
                        {"bool": {"must_not": {"term": {"files_v2": ""}}}}
                    ]
                }
            })
        elif source_type == 'text':
            # text过滤：排除有有效files_v2内容的记录
            # 使用must_not来排除files_v2记录，而不是用filter限制
            self.query_body["query"]["bool"]["must_not"].append({
                "bool": {
                    "must": [
                        {"exists": {"field": "files_v2"}},
                        {"bool": {"must_not": {"term": {"files_v2": ""}}}}
                    ]
                }
            })
        # 注意：如果source_type为None或其他值，不添加任何过滤条件，保持查询所有数据
        return self
    
    def add_fuzzy_search(self, field: str, query: str, fuzziness: str = "AUTO") -> 'ESQueryBuilder':
        """添加模糊搜索"""
        self.query_body["query"]["bool"]["should"].append({
            "fuzzy": {
                field: {
                    "value": query,
                    "fuzziness": fuzziness
                }
            }
        })
        return self
    
    def add_wildcard_search(self, field: str, pattern: str) -> 'ESQueryBuilder':
        """添加通配符搜索"""
        self.query_body["query"]["bool"]["should"].append({
            "wildcard": {
                field: {
                    "value": pattern
                }
            }
        })
        return self
    
    def add_phrase_search(self, field: str, phrase: str, slop: int = 0) -> 'ESQueryBuilder':
        """添加短语搜索"""
        self.query_body["query"]["bool"]["must"].append({
            "match_phrase": {
                field: {
                    "query": phrase,
                    "slop": slop
                }
            }
        })
        return self
    
    def add_range_filter(self, field: str, gte: Any = None, lte: Any = None, 
                         gt: Any = None, lt: Any = None) -> 'ESQueryBuilder':
        """添加范围过滤"""
        range_query = {"range": {field: {}}}
        if gte is not None:
            range_query["range"][field]["gte"] = gte
        if lte is not None:
            range_query["range"][field]["lte"] = lte
        if gt is not None:
            range_query["range"][field]["gt"] = gt
        if lt is not None:
            range_query["range"][field]["lt"] = lt
        
        self.query_body["query"]["bool"]["filter"].append(range_query)
        return self
    
    def add_exists_filter(self, field: str) -> 'ESQueryBuilder':
        """添加字段存在性过滤"""
        self.query_body["query"]["bool"]["filter"].append({
            "exists": {"field": field}
        })
        return self
    
    def add_missing_filter(self, field: str) -> 'ESQueryBuilder':
        """添加字段缺失过滤"""
        self.query_body["query"]["bool"]["must_not"].append({
            "exists": {"field": field}
        })
        return self
    
    def add_terms_filter(self, field: str, values: List[Any]) -> 'ESQueryBuilder':
        """添加术语过滤"""
        self.query_body["query"]["bool"]["filter"].append({
            "terms": {field: values}
        })
        return self
    
    def add_date_range_filter(self, date_range: Dict[str, str]) -> 'ESQueryBuilder':
        """添加日期范围过滤"""
        if date_range and (date_range.get('start') or date_range.get('end')):
            range_filter = {"range": {"public_time": {}}}
            if date_range.get('start'):
                range_filter["range"]["public_time"]["gte"] = date_range['start']
            if date_range.get('end'):
                range_filter["range"]["public_time"]["lte"] = date_range['end']
            self.query_body["query"]["bool"]["filter"].append(range_filter)
        return self
    
    def add_id_search(self, id_list: List[str]) -> 'ESQueryBuilder':
        """添加ID搜索"""
        self.query_body["query"] = {
            "bool": {
                "should": [
                    {"terms": {"id": id_list}},
                    {"terms": {"id.keyword": id_list}},
                    {"terms": {"_id": id_list}}
                ],
                "minimum_should_match": 1
            }
        }
        return self
    
    def set_pagination(self, page: int, page_size: int) -> 'ESQueryBuilder':
        """设置分页"""
        self.query_body["size"] = min(page_size, 10000)  # ES限制
        self.query_body["from"] = (page - 1) * page_size
        return self
    
    def set_cursor_pagination(self, page_size: int, search_after: Optional[List[Any]] = None) -> 'ESQueryBuilder':
        """设置基于游标的分页"""
        self.query_body["size"] = min(page_size, 10000)  # ES限制
        if search_after:
            self.query_body["search_after"] = search_after
        return self
    
    def set_sort(self, sort_field: str = "public_time", sort_order: str = "desc") -> 'ESQueryBuilder':
        """设置排序"""
        self.query_body["sort"] = [{sort_field: {"order": sort_order}}]
        return self
    
    def set_source_fields(self, fields: List[str]) -> 'ESQueryBuilder':
        """设置返回字段"""
        self.query_body["_source"]["includes"] = fields
        return self
    
    def add_highlight(self, fields: List[str]) -> 'ESQueryBuilder':
        """添加高亮 - 优化处理长字段"""
        self.query_body["highlight"] = {
            "fields": {field: {} for field in fields},
            "pre_tags": ['<mark style="background-color: #fff59d; padding: 2px 4px; border-radius: 3px; font-weight: 500;">'],
            "post_tags": ["</mark>"],
            "fragment_size": 150,
            "number_of_fragments": 3,
            "max_analyzed_offset": 500000  # 设置最大分析偏移量
        }
        return self
    
    def add_aggregations(self, aggs: Dict[str, Any]) -> 'ESQueryBuilder':
        """添加聚合"""
        self.query_body["aggs"] = aggs
        return self
    
    def add_script_fields(self, script_fields: Dict[str, Any]) -> 'ESQueryBuilder':
        """添加脚本字段"""
        self.query_body["script_fields"] = script_fields
        return self
    
    def set_min_score(self, min_score: float) -> 'ESQueryBuilder':
        """设置最小匹配分数"""
        self.query_body["min_score"] = min_score
        return self
    
    def add_rescore(self, rescore: Dict[str, Any]) -> 'ESQueryBuilder':
        """添加重排查询"""
        if "rescore" not in self.query_body:
            self.query_body["rescore"] = []
        self.query_body["rescore"].append(rescore)
        return self
    
    def build(self) -> Dict[str, Any]:
        """构建查询体"""
        return self.query_body


class OptimizedESService:
    """优化后的ES服务"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connection_manager = ESConnectionManager()
        self.connection_manager.initialize(config)
        self.cache = ESCache()
        self.logger = logging.getLogger(__name__)
        
        # 默认字段配置
        self.default_source_fields = [
            "title", "text", "files_v2", "public_time", "createdAt",
            "source", "url", "translated_time", "_id"
        ]
        self.default_search_fields = ["files_v2.file_v2_original", "text"]
        self.boost_map = {"files_v2.file_v2_original": 2.0, "text": 1.0}
    
    def search_with_cursor(self,
                          search_keyword: str = "",
                          id_search: str = "",
                          search_fields: Optional[List[str]] = None,
                          order_by: str = "public_time DESC",
                          page_size: int = 50,
                          cursor: Optional[str] = None,
                          content_source_filter: Optional[str] = None,
                          date_range: Optional[Dict[str, str]] = None,
                          enable_highlight: bool = True) -> Dict[str, Any]:
        """
        基于游标的搜索方法
        """
        start_time = time.time()
        query_id = str(uuid.uuid4())
        
        # 参数处理
        if search_fields is None:
            search_fields = self.default_search_fields
            
        page_size = min(page_size, 1000)  # 游标分页限制更小的页面大小
            
        try:
            # 获取ES客户端
            es_client = self.connection_manager.get_client()
            if not es_client:
                raise ConnectionError("无法连接到Elasticsearch")
            
            # 创建游标分页器
            sort_field, sort_order = self._parse_order_by(order_by)
            paginator = CursorPaginator(sort_field, sort_order)
            
            # 解码游标
            cursor_data = paginator.decode_cursor(cursor)
            search_after = paginator.get_search_after(cursor_data) if cursor_data else None
            
            # 构建查询
            builder = ESQueryBuilder(self.config['index'])
            
            # ID搜索优先
            if id_search and id_search.strip():
                id_list = [id_str.strip() for id_str in id_search.split(',') if id_str.strip()]
                builder.add_id_search(id_list)
            else:
                # 关键词搜索
                keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
                if keywords:
                    builder.add_keyword_search(keywords, search_fields, self.boost_map)
                
                # 添加过滤条件
                if content_source_filter:
                    builder.add_content_source_filter(content_source_filter)
                
                if date_range:
                    builder.add_date_range_filter(date_range)
            
            # 设置游标分页和排序
            builder.set_cursor_pagination(page_size, search_after)
            builder.set_sort(sort_field, sort_order)
            
            # 设置返回字段
            builder.set_source_fields(self.default_source_fields)
            
            # 添加高亮
            if enable_highlight and not id_search:
                builder.add_highlight(search_fields)
            
            # 执行查询
            query_body = builder.build()
            
            try:
                response = es_client.search(index=self.config['index'], body=query_body)
            except RequestError as e:
                # 如果是高亮相关错误，禁用高亮重试
                if 'highlight' in str(e) or 'max_analyzed_offset' in str(e):
                    self.logger.warning(f"高亮查询失败，降级到手动高亮: {str(e)}")
                    # 移除高亮配置重试
                    if 'highlight' in query_body:
                        del query_body['highlight']
                    enable_highlight = False  # 标记使用手动高亮
                    response = es_client.search(index=self.config['index'], body=query_body)
                else:
                    raise e
            
            # 处理结果
            total_hits = response['hits']['total']['value']
            hits = response['hits']['hits']
            
            results = []
            cursor_data_list = []
            
            for hit in hits:
                source = hit['_source']
                
                # 处理files_v2内容
                files_v2_content = self._extract_files_v2_content(source.get('files_v2', []))
                
                # 确定内容来源和动态内容
                has_files_v2 = bool(files_v2_content and files_v2_content.strip())
                has_text = bool(source.get('text', '').strip())
                
                if has_files_v2:
                    dynamic_content = files_v2_content
                    content_source = 'files_v2'
                elif has_text:
                    dynamic_content = source.get('text', '')
                    content_source = 'text'
                else:
                    if source.get('files_v2'):
                        dynamic_content = str(source.get('files_v2', ''))
                        content_source = 'files_v2'
                    else:
                        dynamic_content = source.get('text', '')
                        content_source = 'text'
                
                # 构建结果项
                result_item = {
                    'id': hit['_id'],
                    'title': source.get('title', ''),
                    'content': source.get('text', ''),
                    'url': source.get('url', ''),
                    'public_time': source.get('public_time', ''),
                    'source': source.get('source', ''),
                    'files_v2': source.get('files_v2', []),
                    '_score': hit.get('_score', 0),
                    'dynamic_content': dynamic_content,
                    'content_source': content_source,
                    'keywordShow': search_keyword,
                    'keyword': search_keyword,
                    'newsSource': source.get('source', ''),
                    'translated_time': source.get('translated_time', '')
                }

                # 处理日期格式
                result_item.update(self._format_dates(
                    result_item['public_time'],
                    source.get('createdAt', ''),
                    source.get('translated_time', '')
                ))
                
                # 处理高亮
                if enable_highlight and 'highlight' in hit:
                    self._process_highlight(result_item, hit['highlight'])
                else:
                    # 手动高亮处理
                    keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
                    self._manual_highlight(result_item, keywords)
                
                results.append(result_item)
                
                # 为游标分页收集数据
                cursor_data_list.append(paginator.create_cursor_data(hit))
            
            # 构建分页信息
            pagination = paginator.build_pagination_info(
                total_hits, results, page_size, cursor_data_list
            )
            
            execution_time = (time.time() - start_time) * 1000
            self.logger.info(f"ES游标搜索完成，耗时: {execution_time:.2f}ms")
            
            # 记录分析数据
            query_analyzer.analyze_query(
                query_id=query_id,
                query_body=query_body,
                execution_time_ms=execution_time,
                total_hits=total_hits,
                index_name=self.config['index'],
                cache_hit=False,
                aggregation_info=query_body.get("aggs"),
                highlight_info=query_body.get("highlight")
            )
            
            return {
                'data': results,
                'pagination': pagination,
                'cursor': cursor  # 返回当前游标
            }
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"ES游标搜索失败，耗时: {execution_time:.2f}ms, 错误: {str(e)}")
            
            # 记录分析数据（错误情况）
            query_analyzer.analyze_query(
                query_id=query_id,
                query_body={},  # 空查询体表示错误
                execution_time_ms=execution_time,
                total_hits=0,
                index_name=self.config['index'],
                cache_hit=False,
                error=str(e)
            )
            
            raise e
    
    def search(self, 
               search_keyword: str = "",
               id_search: str = "",
               search_fields: Optional[List[str]] = None,
               order_by: str = "public_time DESC",
               page: int = 1,
               page_size: Union[int, str] = 50,
               content_source_filter: Optional[str] = None,
               date_range: Optional[Dict[str, str]] = None,
               enable_highlight: bool = True,
               enable_cache: bool = True) -> Dict[str, Any]:
        """
        优化后的搜索方法
        """
        start_time = time.time()
        query_id = str(uuid.uuid4())
        
        # 参数处理
        if search_fields is None:
            search_fields = self.default_search_fields
        
        # 处理全部数据展示
        show_all = page_size == "all" or (isinstance(page_size, int) and page_size <= 0)
        if show_all:
            actual_page_size = min(10000, 5000)  # 限制最大数量
            actual_page = 1
        else:
            actual_page_size = min(int(page_size), 100)
            actual_page = page
        
        # 检查缓存
        cache_hit = False
        if enable_cache:
            cached_result = self.cache.get(
                search_keyword=search_keyword,
                id_search=id_search,
                page=actual_page,
                page_size=actual_page_size,
                order_by=order_by,
                content_source_filter=content_source_filter,
                date_range=date_range
            )
            if cached_result:
                cache_hit = True
                execution_time = (time.time() - start_time) * 1000
                self.logger.info(f"使用缓存结果，耗时: {execution_time:.2f}ms")
                
                # 记录分析数据（缓存命中）
                query_analyzer.analyze_query(
                    query_id=query_id,
                    query_body={},  # 空查询体表示缓存命中
                    execution_time_ms=execution_time,
                    total_hits=len(cached_result.get('data', [])),
                    index_name=self.config['index'],
                    cache_hit=True
                )
                
                return cached_result
        
        try:
            # 获取ES客户端
            es_client = self.connection_manager.get_client()
            if not es_client:
                raise ConnectionError("无法连接到Elasticsearch")
            
            # 构建查询
            builder = ESQueryBuilder(self.config['index'])
            
            # ID搜索优先
            if id_search and id_search.strip():
                id_list = [id_str.strip() for id_str in id_search.split(',') if id_str.strip()]
                builder.add_id_search(id_list)
            else:
                # 关键词搜索
                keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
                if keywords:
                    builder.add_keyword_search(keywords, search_fields, self.boost_map)
                
                # 添加过滤条件
                if content_source_filter:
                    builder.add_content_source_filter(content_source_filter)
                
                if date_range:
                    builder.add_date_range_filter(date_range)
            
            # 设置分页和排序
            builder.set_pagination(actual_page, actual_page_size)
            
            # 解析排序
            sort_field, sort_order = self._parse_order_by(order_by)
            builder.set_sort(sort_field, sort_order)
            
            # 设置返回字段
            builder.set_source_fields(self.default_source_fields)
            
            # 添加高亮
            if enable_highlight and not id_search:
                builder.add_highlight(search_fields)
            
            # 执行查询
            query_body = builder.build()
            
            try:
                response = es_client.search(index=self.config['index'], body=query_body)
            except RequestError as e:
                # 如果是高亮相关错误，禁用高亮重试
                if 'highlight' in str(e) or 'max_analyzed_offset' in str(e):
                    self.logger.warning(f"高亮查询失败，降级到手动高亮: {str(e)}")
                    # 移除高亮配置重试
                    if 'highlight' in query_body:
                        del query_body['highlight']
                    enable_highlight = False  # 标记使用手动高亮
                    response = es_client.search(index=self.config['index'], body=query_body)
                else:
                    raise e
            
            # 处理结果
            result = self._process_search_response(
                response, 
                search_keyword, 
                page, 
                page_size, 
                show_all,
                enable_highlight
            )
            
            # 缓存结果
            if enable_cache:
                self.cache.set(
                    result,
                    search_keyword=search_keyword,
                    id_search=id_search,
                    page=actual_page,
                    page_size=actual_page_size,
                    order_by=order_by,
                    content_source_filter=content_source_filter,
                    date_range=date_range
                )
            
            execution_time = (time.time() - start_time) * 1000
            self.logger.info(f"ES搜索完成，耗时: {execution_time:.2f}ms")
            
            # 记录分析数据
            query_analyzer.analyze_query(
                query_id=query_id,
                query_body=query_body,
                execution_time_ms=execution_time,
                total_hits=response['hits']['total']['value'],
                index_name=self.config['index'],
                cache_hit=False,
                aggregation_info=query_body.get("aggs"),
                highlight_info=query_body.get("highlight")
            )
            
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"ES搜索失败，耗时: {execution_time:.2f}ms, 错误: {str(e)}")
            
            # 记录分析数据（错误情况）
            query_analyzer.analyze_query(
                query_id=query_id,
                query_body={},  # 空查询体表示错误
                execution_time_ms=execution_time,
                total_hits=0,
                index_name=self.config['index'],
                cache_hit=False,
                error=str(e)
            )
            
            raise e
    
    def _parse_order_by(self, order_by: str) -> tuple:
        """解析排序参数"""
        parts = order_by.split()
        if len(parts) >= 2:
            field = parts[0]
            order = parts[1].lower()
        else:
            field = "public_time"
            order = "desc"
        
        # 字段映射
        field_mapping = {
            "createdAt": "public_time",
            "MonitorDate": "public_time"
        }
        
        field = field_mapping.get(field, field)
        return field, order
    
    def _process_search_response(self, response: Dict[str, Any], 
                               search_keyword: str,
                               page: int,
                               page_size: Union[int, str],
                               show_all: bool,
                               enable_highlight: bool) -> Dict[str, Any]:
        """处理搜索响应"""
        total_hits = response['hits']['total']['value']
        hits = response['hits']['hits']
        
        results = []
        for hit in hits:
            source = hit['_source']
            
            # 处理files_v2内容
            files_v2_content = self._extract_files_v2_content(source.get('files_v2', []))
            
            # 确定内容来源和动态内容 - 修复ID搜索时内容来源错误的问题
            # 更严格的内容来源判断逻辑
            has_files_v2 = bool(files_v2_content and files_v2_content.strip())
            has_text = bool(source.get('text', '').strip())
            
            if has_files_v2:
                dynamic_content = files_v2_content
                content_source = 'files_v2'
            elif has_text:
                dynamic_content = source.get('text', '')
                content_source = 'text'
            else:
                # 如果都没有内容，检查原始数据结构
                if source.get('files_v2'):
                    dynamic_content = str(source.get('files_v2', ''))
                    content_source = 'files_v2'
                else:
                    dynamic_content = source.get('text', '')
                    content_source = 'text'
            
            # 构建结果项
            result_item = {
                'id': hit['_id'],
                'title': source.get('title', ''),
                'content': source.get('text', ''),
                'url': source.get('url', ''),
                'public_time': source.get('public_time', ''),
                'source': source.get('source', ''),
                'files_v2': source.get('files_v2', []),
                '_score': hit.get('_score', 0),
                'dynamic_content': dynamic_content,
                'content_source': content_source,
                'keywordShow': search_keyword,
                'keyword': search_keyword,
                'newsSource': source.get('source', ''),
                'translated_time': source.get('translated_time', '')  # 添加翻译时间字段
            }

            # 处理日期格式 - 传入createdAt和translated_time字段
            result_item.update(self._format_dates(
                result_item['public_time'],
                source.get('createdAt', ''),
                source.get('translated_time', '')
            ))
            
            # 处理高亮
            # 处理高亮
            if enable_highlight and 'highlight' in hit:
                self._process_highlight(result_item, hit['highlight'])
            else:
                # 手动高亮处理
                keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
                self._manual_highlight(result_item, keywords)
            
            results.append(result_item)
        
        # 构建分页信息
        if show_all:
            pagination = {
                'current_page': 1,
                'page_size': 'all',
                'total_count': total_hits,
                'total_pages': 1,
                'has_next': False,
                'has_prev': False,
                'show_all': True,
                'actual_displayed': len(results)
            }
        else:
            pagination = {
                'current_page': page,
                'page_size': page_size,
                'total_count': total_hits,
                'total_pages': (total_hits + page_size - 1) // page_size,
                'has_next': page * page_size < total_hits,
                'has_prev': page > 1,
                'show_all': False
            }
        
        return {
            'data': results,
            'pagination': pagination
        }
    
    def _build_default_aggregations(self) -> Dict[str, Any]:
        """构建默认聚合查询"""
        return {
            "content_source_stats": {
                "terms": {
                    "script": {
                        "source": """
                        if (doc.containsKey('files_v2') && !doc['files_v2'].empty) {
                            return 'files_v2';
                        } else {
                            return 'text';
                        }
                        """
                    }
                }
            },
            "date_histogram": {
                "date_histogram": {
                    "field": "public_time",
                    "calendar_interval": "day"
                }
            },
            "source_stats": {
                "terms": {
                    "field": "source.keyword",
                    "size": 10
                }
            }
        }
    
    def get_query_performance_report(self) -> Dict[str, Any]:
        """获取查询性能报告"""
        return query_analyzer.get_performance_report()
    
    def export_query_analysis_data(self) -> str:
        """导出查询分析数据"""
        return query_analyzer.export_analysis_data()
    
    def clear_query_analysis_history(self):
        """清空查询分析历史"""
        query_analyzer.clear_history()
    
    def _format_dates(self, public_time: str, created_at: str = None, translated_time: str = None) -> Dict[str, str]:
        """格式化日期"""
        result = {}

        # 处理MonitorDate (使用public_time)
        if public_time:
            try:
                if isinstance(public_time, str):
                    dt = datetime.fromisoformat(public_time.replace('Z', '+00:00'))
                    result['MonitorDate'] = dt.strftime('%Y年%m月%d日')
                else:
                    result['MonitorDate'] = str(public_time)
            except:
                result['MonitorDate'] = str(public_time)
        else:
            result['MonitorDate'] = ''

        # 处理createdAt (使用ES中的createdAt字段)
        if created_at:
            try:
                if isinstance(created_at, str):
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    result['createdAt'] = dt.strftime('%Y年%m月%d日')  # 修改为只显示年月日
                else:
                    result['createdAt'] = str(created_at)
            except:
                result['createdAt'] = str(created_at)
        else:
            # 如果没有createdAt字段，回退到使用public_time
            result['createdAt'] = result['MonitorDate']
            
        # 处理翻译时间
        if translated_time:
            try:
                if isinstance(translated_time, str):
                    dt = datetime.fromisoformat(translated_time.replace('Z', '+00:00'))
                    result['translated_time'] = dt.strftime('%Y年%m月%d日')  # 只显示年月日
                else:
                    result['translated_time'] = str(translated_time)
            except:
                result['translated_time'] = str(translated_time)
        else:
            result['translated_time'] = ''

        return result
    
    def _process_highlight(self, result_item: Dict, highlight: Dict):
        """处理ES高亮结果"""
        for field, fragments in highlight.items():
            if fragments:
                highlighted_text = ' | '.join(fragments)
                if field == 'files_v2.file_v2_original':
                    result_item['dynamic_content_highlighted'] = highlighted_text
                elif field == 'text':
                    result_item['content_highlighted'] = highlighted_text
                elif field == 'title':
                    result_item['title_highlighted'] = highlighted_text
    
    def _manual_highlight(self, result_item: Dict, keywords: List[str]):
        """手动高亮处理"""
        for key in ['title', 'content']:
            if result_item.get(key):
                result_item[f"{key}_highlighted"] = self._highlight_text(
                    result_item[key], keywords
                )
        
        if result_item.get('dynamic_content'):
            result_item['dynamic_content_highlighted'] = self._highlight_text(
                result_item['dynamic_content'], keywords
            )
    
    def _highlight_text(self, text: str, keywords: List[str], context_length: int = 10) -> str:
        """高亮文本"""
        if not text or not keywords:
            return text
        
        import re
        
        # 找到所有关键字的位置
        matches = []
        for keyword in keywords:
            if keyword:
                for match in re.finditer(re.escape(keyword), text, re.IGNORECASE):
                    matches.append((match.start(), match.end(), keyword))
        
        if not matches:
            return text[:50] + "..." if len(text) > 50 else text
        
        # 按位置排序
        matches.sort(key=lambda x: x[0])
        
        # 合并重叠的匹配区域
        merged_segments = []
        for start, end, keyword in matches:
            context_start = max(0, start - context_length)
            context_end = min(len(text), end + context_length)
            
            # 检查是否与已有片段重叠
            merged = False
            for i, (seg_start, seg_end, _) in enumerate(merged_segments):
                if context_start <= seg_end and context_end >= seg_start:
                    new_start = min(seg_start, context_start)
                    new_end = max(seg_end, context_end)
                    new_text = text[new_start:new_end]
                    merged_segments[i] = (new_start, new_end, new_text)
                    merged = True
                    break
            
            if not merged:
                merged_segments.append((context_start, context_end, text[context_start:context_end]))
        
        # 高亮关键字
        result_segments = []
        for start, end, segment_text in merged_segments:
            highlighted_text = segment_text
            for keyword in keywords:
                if keyword:
                    pattern = re.compile(re.escape(keyword), re.IGNORECASE)
                    highlighted_text = pattern.sub(
                        f'<mark style="background-color: #fff59d; padding: 2px 4px; border-radius: 3px; font-weight: 500;">{keyword}</mark>',
                        highlighted_text
                    )
            
            prefix = "..." if start > 0 else ""
            suffix = "..." if end < len(text) else ""
            result_segments.append(f"{prefix}{highlighted_text}{suffix}")
        
        return " | ".join(result_segments)
    
    def search_with_aggregations(self, 
                               search_keyword: str = "",
                               aggs: Optional[Dict[str, Any]] = None,
                               **kwargs) -> Dict[str, Any]:
        """带聚合的搜索"""
        # 获取基础搜索结果
        result = self.search(search_keyword=search_keyword, **kwargs)
        
        if not aggs:
            return result
        
        try:
            es_client = self.connection_manager.get_client()
            if not es_client:
                raise ConnectionError("无法连接到Elasticsearch")
            
            # 构建聚合查询
            builder = ESQueryBuilder(self.config['index'])
            keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
            if keywords:
                builder.add_keyword_search(keywords, self.default_search_fields, self.boost_map)
            
            builder.add_aggregations(aggs)
            query_body = builder.build()
            
            # 执行聚合查询
            agg_response = es_client.search(index=self.config['index'], body=query_body)
            
            # 添加聚合结果
            result['aggregations'] = agg_response.get('aggregations', {})
            
        except Exception as e:
            self.logger.error(f"聚合查询失败: {str(e)}")
            result['aggregations'] = {}
        
        return result
    
    def scroll_search(self, 
                     search_keyword: str = "",
                     scroll_size: int = 1000,
                     scroll_timeout: str = None,
                     **kwargs) -> Dict[str, Any]:
        """滚动搜索 - 处理大量数据"""
        try:
            # 从环境变量获取滚动超时配置
            if scroll_timeout is None:
                scroll_timeout = os.getenv('ES_SCROLL_TIMEOUT', '5m')

            es_client = self.connection_manager.get_client()
            if not es_client:
                raise ConnectionError("无法连接到Elasticsearch")
            
            # 构建查询
            builder = ESQueryBuilder(self.config['index'])
            keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
            if keywords:
                builder.add_keyword_search(keywords, self.default_search_fields, self.boost_map)
            
            builder.set_pagination(1, scroll_size)
            builder.set_source_fields(self.default_source_fields)
            
            query_body = builder.build()
            
            # 初始化滚动搜索
            response = es_client.search(
                index=self.config['index'],
                body=query_body,
                scroll=scroll_timeout
            )
            
            scroll_id = response['_scroll_id']
            total_hits = response['hits']['total']['value']
            all_results = []
            
            # 处理第一批结果
            for hit in response['hits']['hits']:
                all_results.append(self._process_hit(hit, search_keyword))
            
            # 继续滚动获取剩余数据
            while len(response['hits']['hits']) > 0:
                response = es_client.scroll(scroll_id=scroll_id, scroll=scroll_timeout)
                
                for hit in response['hits']['hits']:
                    all_results.append(self._process_hit(hit, search_keyword))
                
                if len(response['hits']['hits']) == 0:
                    break
            
            # 清理滚动上下文
            try:
                es_client.clear_scroll(scroll_id=scroll_id)
            except:
                pass
            
            return {
                'data': all_results,
                'total_count': total_hits,
                'scroll_complete': True
            }
            
        except Exception as e:
            self.logger.error(f"滚动搜索失败: {str(e)}")
            raise e
    
    def _process_hit(self, hit: Dict, search_keyword: str) -> Dict:
        """处理单个搜索结果"""
        source = hit['_source']
        
        # 提取files_v2内容
        files_v2_content = self._extract_files_v2_content(source.get('files_v2', []))
        
        # 确定内容来源和动态内容
        if files_v2_content:
            dynamic_content = files_v2_content
            content_source = 'files_v2'
        else:
            dynamic_content = source.get('text', '')
            content_source = 'text'
        
        # 构建结果项
        result_item = {
            'id': hit['_id'],
            'title': source.get('title', ''),
            'content': source.get('text', ''),
            'url': source.get('url', ''),
            'public_time': source.get('public_time', ''),
            'source': source.get('source', ''),
            'files_v2': source.get('files_v2', []),
            '_score': hit.get('_score', 0),
            'dynamic_content': dynamic_content,
            'content_source': content_source,
            'keywordShow': search_keyword,
            'keyword': search_keyword,
            'newsSource': source.get('source', ''),
            'translated_time': source.get('translated_time', '')  # 添加翻译时间字段
        }

        # 处理日期格式 - 传入createdAt和translated_time字段
        result_item.update(self._format_dates(
            result_item['public_time'],
            source.get('createdAt', ''),
            source.get('translated_time', '')
        ))
        
        # 手动高亮处理
        keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
        self._manual_highlight(result_item, keywords)
        
        return result_item
    
    def get_statistics(self, search_keyword: str = "") -> Dict[str, Any]:
        """获取搜索统计信息"""
        aggs = {
            "content_source_stats": {
                "terms": {
                    "script": {
                        "source": """
                        if (doc.containsKey('files_v2') && !doc['files_v2'].empty) {
                            return 'files_v2';
                        } else {
                            return 'text';
                        }
                        """
                    }
                }
            },
            "date_histogram": {
                "date_histogram": {
                    "field": "public_time",
                    "calendar_interval": "day"
                }
            },
            "source_stats": {
                "terms": {
                    "field": "source.keyword",
                    "size": 10
                }
            }
        }
        
        result = self.search_with_aggregations(
            search_keyword=search_keyword,
            aggs=aggs,
            page_size=0  # 只获取聚合结果
        )
        
        return result.get('aggregations', {})
    
    def search_with_cursor(self,
                          search_keyword: str = "",
                          id_search: str = "",
                          search_fields: Optional[List[str]] = None,
                          order_by: str = "public_time DESC",
                          page_size: int = 50,
                          cursor: Optional[str] = None,
                          content_source_filter: Optional[str] = None,
                          date_range: Optional[Dict[str, str]] = None,
                          enable_highlight: bool = True) -> Dict[str, Any]:
        """
        基于游标的搜索方法
        """
        start_time = time.time()
        query_id = str(uuid.uuid4())
        
        # 参数处理
        if search_fields is None:
            search_fields = self.default_search_fields
            
        page_size = min(page_size, 1000)  # 游标分页限制更小的页面大小
            
        try:
            # 获取ES客户端
            es_client = self.connection_manager.get_client()
            if not es_client:
                raise ConnectionError("无法连接到Elasticsearch")
            
            # 创建游标分页器
            sort_field, sort_order = self._parse_order_by(order_by)
            paginator = CursorPaginator(sort_field, sort_order)
            
            # 解码游标
            cursor_data = paginator.decode_cursor(cursor)
            search_after = paginator.get_search_after(cursor_data) if cursor_data else None
            
            # 构建查询
            builder = ESQueryBuilder(self.config['index'])
            
            # ID搜索优先
            if id_search and id_search.strip():
                id_list = [id_str.strip() for id_str in id_search.split(',') if id_str.strip()]
                builder.add_id_search(id_list)
            else:
                # 关键词搜索
                keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
                if keywords:
                    builder.add_keyword_search(keywords, search_fields, self.boost_map)
                
                # 添加过滤条件
                if content_source_filter:
                    builder.add_content_source_filter(content_source_filter)
                
                if date_range:
                    builder.add_date_range_filter(date_range)
            
            # 设置游标分页和排序
            builder.set_cursor_pagination(page_size, search_after)
            builder.set_sort(sort_field, sort_order)
            
            # 设置返回字段
            builder.set_source_fields(self.default_source_fields)
            
            # 添加高亮
            if enable_highlight and not id_search:
                builder.add_highlight(search_fields)
            
            # 执行查询
            query_body = builder.build()
            
            try:
                response = es_client.search(index=self.config['index'], body=query_body)
            except RequestError as e:
                # 如果是高亮相关错误，禁用高亮重试
                if 'highlight' in str(e) or 'max_analyzed_offset' in str(e):
                    self.logger.warning(f"高亮查询失败，降级到手动高亮: {str(e)}")
                    # 移除高亮配置重试
                    if 'highlight' in query_body:
                        del query_body['highlight']
                    enable_highlight = False  # 标记使用手动高亮
                    response = es_client.search(index=self.config['index'], body=query_body)
                else:
                    raise e
            
            # 处理结果
            total_hits = response['hits']['total']['value']
            hits = response['hits']['hits']
            
            results = []
            cursor_data_list = []
            
            for hit in hits:
                source = hit['_source']
                
                # 处理files_v2内容
                files_v2_content = self._extract_files_v2_content(source.get('files_v2', []))
                
                # 确定内容来源和动态内容
                has_files_v2 = bool(files_v2_content and files_v2_content.strip())
                has_text = bool(source.get('text', '').strip())
                
                if has_files_v2:
                    dynamic_content = files_v2_content
                    content_source = 'files_v2'
                elif has_text:
                    dynamic_content = source.get('text', '')
                    content_source = 'text'
                else:
                    if source.get('files_v2'):
                        dynamic_content = str(source.get('files_v2', ''))
                        content_source = 'files_v2'
                    else:
                        dynamic_content = source.get('text', '')
                        content_source = 'text'
                
                # 构建结果项
                result_item = {
                    'id': hit['_id'],
                    'title': source.get('title', ''),
                    'content': source.get('text', ''),
                    'url': source.get('url', ''),
                    'public_time': source.get('public_time', ''),
                    'source': source.get('source', ''),
                    'files_v2': source.get('files_v2', []),
                    '_score': hit.get('_score', 0),
                    'dynamic_content': dynamic_content,
                    'content_source': content_source,
                    'keywordShow': search_keyword,
                    'keyword': search_keyword,
                    'newsSource': source.get('source', ''),
                    'translated_time': source.get('translated_time', '')
                }

                # 处理日期格式
                result_item.update(self._format_dates(
                    result_item['public_time'],
                    source.get('createdAt', ''),
                    source.get('translated_time', '')
                ))
                
                # 处理高亮
                if enable_highlight and 'highlight' in hit:
                    self._process_highlight(result_item, hit['highlight'])
                else:
                    # 手动高亮处理
                    keywords = [kw.strip() for kw in search_keyword.split(',') if kw.strip()]
                    self._manual_highlight(result_item, keywords)
                
                results.append(result_item)
                
                # 为游标分页收集数据
                cursor_data_list.append(paginator.create_cursor_data(hit))
            
            # 构建分页信息
            pagination = paginator.build_pagination_info(
                total_hits, results, page_size, cursor_data_list
            )
            
            execution_time = (time.time() - start_time) * 1000
            self.logger.info(f"ES游标搜索完成，耗时: {execution_time:.2f}ms")
            
            # 记录分析数据
            query_analyzer.analyze_query(
                query_id=query_id,
                query_body=query_body,
                execution_time_ms=execution_time,
                total_hits=total_hits,
                index_name=self.config['index'],
                cache_hit=False,
                aggregation_info=query_body.get("aggs"),
                highlight_info=query_body.get("highlight")
            )
            
            return {
                'data': results,
                'pagination': pagination,
                'cursor': cursor  # 返回当前游标
            }
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self.logger.error(f"ES游标搜索失败，耗时: {execution_time:.2f}ms, 错误: {str(e)}")
            
            # 记录分析数据（错误情况）
            query_analyzer.analyze_query(
                query_id=query_id,
                query_body={},  # 空查询体表示错误
                execution_time_ms=execution_time,
                total_hits=0,
                index_name=self.config['index'],
                cache_hit=False,
                error=str(e)
            )
            
            raise e
    
    def _extract_files_v2_content(self, files_v2_data: List[Dict]) -> str:
        """提取files_v2内容 - 修复内容来源判断问题"""
        if not files_v2_data:
            return ''
        
        # 处理不同的files_v2数据格式
        if isinstance(files_v2_data, list):
            content_parts = []
            for file_item in files_v2_data:
                if isinstance(file_item, dict):
                    # 检查多种可能的字段名
                    content = (file_item.get('file_v2_original', '') or 
                              file_item.get('content', '') or 
                              file_item.get('text', ''))
                    if content and content.strip():
                        content_parts.append(content.strip())
                elif isinstance(file_item, str) and file_item.strip():
                    content_parts.append(file_item.strip())
            
            return ' '.join(content_parts)
        
        elif isinstance(files_v2_data, str):
            return files_v2_data.strip()
        
        return ''
    
    def get_query_templates(self) -> List[Dict[str, Any]]:
        """获取所有查询模板"""
        return template_manager.list_templates()
    
    def get_query_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取指定查询模板"""
        template = template_manager.get_template(template_id)
        return template.__dict__ if template else None
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            es_client = self.connection_manager.get_client()
            if not es_client:
                return {
                    'status': 'error',
                    'message': '无法连接到Elasticsearch',
                    'timestamp': datetime.now().isoformat()
                }
            
            # 检查集群健康状态
            cluster_health = es_client.cluster.health()

            # 检查索引状态 - 使用count方法代替stats方法
            doc_count = es_client.count(index=self.config['index'])

            return {
                'status': 'healthy',
                'cluster_status': cluster_health['status'],
                'cluster_name': cluster_health['cluster_name'],
                'number_of_nodes': cluster_health['number_of_nodes'],
                'index_name': self.config['index'],
                'index_docs_count': doc_count['count'],
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }


# 使用示例
if __name__ == "__main__":
    # ES配置 - 从环境变量读取
    import os
    from dotenv import load_dotenv
    load_dotenv()

    # 检查必需的环境变量
    required_vars = ['ES_HOST', 'ES_USERNAME', 'ES_PASSWORD', 'ES_INDEX']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
        exit(1)

    es_config = {
        'host': os.getenv('ES_HOST'),
        'port': int(os.getenv('ES_PORT', '9200')),
        'username': os.getenv('ES_USERNAME'),
        'password': os.getenv('ES_PASSWORD'),
        'index': os.getenv('ES_INDEX')
    }
    
    # 创建服务实例
    es_service = OptimizedESService(es_config)
    
    # 基础搜索
    result = es_service.search(
        search_keyword="Wickersham,Trump",
        page=1,
        page_size=10,
        content_source_filter='files_v2',
        enable_highlight=True
    )
    
    print(f"找到 {len(result['data'])} 条记录")
    
    # 带聚合的搜索
    agg_result = es_service.search_with_aggregations(
        search_keyword="美国",
        aggs={
            "source_count": {
                "terms": {"field": "source.keyword"}
            }
        }
    )
    
    # 健康检查
    health = es_service.health_check()
    print(f"ES健康状态: {health['status']}")
