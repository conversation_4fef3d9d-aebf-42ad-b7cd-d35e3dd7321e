#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询模板模块 - 支持常用查询模式的快速构建
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum


class QueryTemplateType(Enum):
    """查询模板类型"""
    KEYWORD_SEARCH = "keyword_search"
    DATE_RANGE_SEARCH = "date_range_search"
    CONTENT_SOURCE_FILTER = "content_source_filter"
    ID_SEARCH = "id_search"
    AGGREGATION_STATS = "aggregation_stats"
    CUSTOM = "custom"


@dataclass
class QueryTemplate:
    """查询模板数据结构"""
    template_id: str
    name: str
    description: str
    template_type: QueryTemplateType
    parameters: Dict[str, Any]
    query_body: Dict[str, Any]


class QueryTemplateManager:
    """查询模板管理器"""
    
    def __init__(self):
        self.templates: Dict[str, QueryTemplate] = {}
        self._initialize_default_templates()
    
    def _initialize_default_templates(self):
        """初始化默认查询模板"""
        # 基础关键词搜索模板
        keyword_template = QueryTemplate(
            template_id="keyword_search_basic",
            name="基础关键词搜索",
            description="支持多关键词跨字段搜索的模板",
            template_type=QueryTemplateType.KEYWORD_SEARCH,
            parameters={
                "keywords": [],
                "fields": ["files_v2.file_v2_original", "text"],
                "boost_map": {"files_v2.file_v2_original": 2.0, "text": 1.0}
            },
            query_body={
                "query": {
                    "bool": {
                        "should": [],
                        "minimum_should_match": 1
                    }
                }
            }
        )
        self.templates[keyword_template.template_id] = keyword_template
        
        # 日期范围搜索模板
        date_range_template = QueryTemplate(
            template_id="date_range_search",
            name="日期范围搜索",
            description="按日期范围过滤的搜索模板",
            template_type=QueryTemplateType.DATE_RANGE_SEARCH,
            parameters={
                "date_field": "public_time",
                "start_date": None,
                "end_date": None
            },
            query_body={
                "query": {
                    "bool": {
                        "filter": [{
                            "range": {
                                "public_time": {}
                            }
                        }]
                    }
                }
            }
        )
        self.templates[date_range_template.template_id] = date_range_template
        
        # 内容源过滤模板
        content_source_template = QueryTemplate(
            template_id="content_source_filter",
            name="内容源过滤",
            description="按内容来源过滤的搜索模板",
            template_type=QueryTemplateType.CONTENT_SOURCE_FILTER,
            parameters={
                "source_type": "files_v2"
            },
            query_body={
                "query": {
                    "bool": {
                        "filter": []
                    }
                }
            }
        )
        self.templates[content_source_template.template_id] = content_source_template
        
        # ID搜索模板
        id_search_template = QueryTemplate(
            template_id="id_search",
            name="ID搜索",
            description="根据ID列表搜索的模板",
            template_type=QueryTemplateType.ID_SEARCH,
            parameters={
                "id_list": []
            },
            query_body={
                "query": {
                    "bool": {
                        "should": [
                            {"terms": {"id": []}},
                            {"terms": {"id.keyword": []}},
                            {"terms": {"_id": []}}
                        ],
                        "minimum_should_match": 1
                    }
                }
            }
        )
        self.templates[id_search_template.template_id] = id_search_template
        
        # 聚合统计模板
        aggregation_template = QueryTemplate(
            template_id="aggregation_stats",
            name="聚合统计",
            description="用于获取内容来源、时间分布等统计信息的聚合模板",
            template_type=QueryTemplateType.AGGREGATION_STATS,
            parameters={
                "agg_types": ["content_source", "date_histogram", "source_stats"]
            },
            query_body={
                "aggs": {},
                "size": 0
            }
        )
        self.templates[aggregation_template.template_id] = aggregation_template
    
    def get_template(self, template_id: str) -> Optional[QueryTemplate]:
        """获取查询模板"""
        return self.templates.get(template_id)
    
    def list_templates(self) -> List[Dict[str, Any]]:
        """列出所有查询模板"""
        return [asdict(template) for template in self.templates.values()]
    
    def create_template(self, template: QueryTemplate) -> bool:
        """创建新的查询模板"""
        if template.template_id in self.templates:
            return False
        self.templates[template.template_id] = template
        return True
    
    def update_template(self, template_id: str, template: QueryTemplate) -> bool:
        """更新查询模板"""
        if template_id not in self.templates:
            return False
        self.templates[template_id] = template
        return True
    
    def delete_template(self, template_id: str) -> bool:
        """删除查询模板"""
        if template_id not in self.templates:
            return False
        if template_id in ["keyword_search_basic", "date_range_search", "content_source_filter", 
                          "id_search", "aggregation_stats"]:
            # 不允许删除默认模板
            return False
        del self.templates[template_id]
        return True


# 全局查询模板管理器实例
template_manager = QueryTemplateManager()