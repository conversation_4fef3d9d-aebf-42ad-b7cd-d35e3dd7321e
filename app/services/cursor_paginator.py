#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于游标的分页模块
"""

import base64
import json
from typing import Dict, Any, Optional, List
from datetime import datetime


class CursorPaginator:
    """基于游标的分页器"""
    
    def __init__(self, sort_field: str = "_id", sort_order: str = "asc"):
        self.sort_field = sort_field
        self.sort_order = sort_order.lower()
        
    def encode_cursor(self, data: Dict[str, Any]) -> str:
        """编码游标"""
        try:
            json_str = json.dumps(data, separators=(',', ':'))
            return base64.urlsafe_b64encode(json_str.encode('utf-8')).decode('utf-8')
        except Exception:
            return ""
    
    def decode_cursor(self, cursor: str) -> Optional[Dict[str, Any]]:
        """解码游标"""
        try:
            if not cursor:
                return None
            json_str = base64.urlsafe_b64decode(cursor.encode('utf-8')).decode('utf-8')
            return json.loads(json_str)
        except Exception:
            return None
    
    def get_search_after(self, cursor_data: Optional[Dict[str, Any]]) -> Optional[List[Any]]:
        """获取search_after参数"""
        if not cursor_data:
            return None
        # 根据排序字段获取值
        sort_values = []
        if self.sort_field in cursor_data:
            sort_values.append(cursor_data[self.sort_field])
        # 总是添加_id作为二级排序字段以确保唯一性
        if "_id" in cursor_data:
            sort_values.append(cursor_data["_id"])
        return sort_values if sort_values else None
    
    def create_cursor_data(self, hit: Dict[str, Any]) -> Dict[str, Any]:
        """从ES命中创建游标数据"""
        source = hit.get('_source', {})
        cursor_data = {}
        
        # 添加排序字段
        if self.sort_field in source:
            cursor_data[self.sort_field] = source[self.sort_field]
        elif self.sort_field in hit:
            cursor_data[self.sort_field] = hit[self.sort_field]
            
        # 总是添加_id以确保唯一性
        cursor_data["_id"] = hit.get('_id')
        
        return cursor_data
    
    def build_pagination_info(self, 
                             total_hits: int,
                             results: List[Dict[str, Any]],
                             page_size: int,
                             cursor_data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """构建分页信息"""
        has_next = len(results) >= page_size
        has_prev = False  # 游标分页通常不提供向前导航
        
        # 生成下一个游标（如果有更多数据）
        next_cursor = None
        if has_next and cursor_data_list:
            # 使用最后一个结果作为下一个游标的起点
            next_cursor = self.encode_cursor(cursor_data_list[-1])
        
        return {
            'total_count': total_hits,
            'page_size': page_size,
            'has_next': has_next,
            'has_prev': has_prev,
            'next_cursor': next_cursor,
            'sort_field': self.sort_field,
            'sort_order': self.sort_order
        }