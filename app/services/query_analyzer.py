#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询性能分析模块 - 提供查询性能分析报告
"""

import time
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import threading


@dataclass
class QueryAnalysis:
    """查询分析数据结构"""
    query_id: str
    timestamp: datetime
    query_body: Dict[str, Any]
    execution_time_ms: float
    total_hits: int
    index_name: str
    cache_hit: bool
    error: Optional[str] = None
    aggregation_info: Optional[Dict[str, Any]] = None
    highlight_info: Optional[Dict[str, Any]] = None


class QueryAnalyzer:
    """查询分析器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.query_history: List[QueryAnalysis] = []
        self._lock = threading.Lock()
        self.enabled = True
    
    def analyze_query(self, query_id: str, query_body: Dict[str, Any], 
                     execution_time_ms: float, total_hits: int, 
                     index_name: str, cache_hit: bool = False,
                     error: Optional[str] = None,
                     aggregation_info: Optional[Dict[str, Any]] = None,
                     highlight_info: Optional[Dict[str, Any]] = None) -> QueryAnalysis:
        """分析查询并记录"""
        if not self.enabled:
            return None
            
        analysis = QueryAnalysis(
            query_id=query_id,
            timestamp=datetime.now(),
            query_body=query_body,
            execution_time_ms=execution_time_ms,
            total_hits=total_hits,
            index_name=index_name,
            cache_hit=cache_hit,
            error=error,
            aggregation_info=aggregation_info,
            highlight_info=highlight_info
        )
        
        with self._lock:
            self.query_history.append(analysis)
            # 保持历史记录在限制范围内
            if len(self.query_history) > self.max_history:
                self.query_history = self.query_history[-self.max_history:]
        
        return analysis
    
    def get_performance_report(self, limit: int = 50) -> Dict[str, Any]:
        """生成性能报告"""
        with self._lock:
            if not self.query_history:
                return {"message": "暂无查询历史记录"}
            
            # 获取最近的查询记录
            recent_queries = self.query_history[-limit:]
            
            # 计算统计数据
            total_queries = len(recent_queries)
            avg_execution_time = sum(q.execution_time_ms for q in recent_queries) / total_queries
            max_execution_time = max(q.execution_time_ms for q in recent_queries)
            min_execution_time = min(q.execution_time_ms for q in recent_queries)
            
            # 缓存命中率
            cache_hits = sum(1 for q in recent_queries if q.cache_hit)
            cache_hit_rate = cache_hits / total_queries if total_queries > 0 else 0
            
            # 错误率
            errors = sum(1 for q in recent_queries if q.error)
            error_rate = errors / total_queries if total_queries > 0 else 0
            
            # 按执行时间排序的慢查询
            slow_queries = sorted(recent_queries, key=lambda x: x.execution_time_ms, reverse=True)[:10]
            
            # 聚合查询统计
            aggregation_queries = [q for q in recent_queries if q.aggregation_info]
            aggregation_rate = len(aggregation_queries) / total_queries if total_queries > 0 else 0
            
            # 高亮查询统计
            highlight_queries = [q for q in recent_queries if q.highlight_info]
            highlight_rate = len(highlight_queries) / total_queries if total_queries > 0 else 0
            
            return {
                "summary": {
                    "total_queries": total_queries,
                    "avg_execution_time_ms": round(avg_execution_time, 2),
                    "max_execution_time_ms": max_execution_time,
                    "min_execution_time_ms": min_execution_time,
                    "cache_hit_rate": round(cache_hit_rate * 100, 2),
                    "error_rate": round(error_rate * 100, 2),
                    "aggregation_query_rate": round(aggregation_rate * 100, 2),
                    "highlight_query_rate": round(highlight_rate * 100, 2)
                },
                "slow_queries": [
                    {
                        "query_id": q.query_id,
                        "execution_time_ms": q.execution_time_ms,
                        "total_hits": q.total_hits,
                        "timestamp": q.timestamp.isoformat(),
                        "has_error": bool(q.error),
                        "cache_hit": q.cache_hit
                    }
                    for q in slow_queries
                ],
                "query_distribution": self._analyze_query_distribution(recent_queries)
            }
    
    def _analyze_query_distribution(self, queries: List[QueryAnalysis]) -> Dict[str, Any]:
        """分析查询分布"""
        distribution = {
            "by_hour": {},
            "by_index": {},
            "by_query_type": {}
        }
        
        for query in queries:
            # 按小时分布
            hour = query.timestamp.hour
            distribution["by_hour"][hour] = distribution["by_hour"].get(hour, 0) + 1
            
            # 按索引分布
            index = query.index_name
            distribution["by_index"][index] = distribution["by_index"].get(index, 0) + 1
            
            # 按查询类型分布（基于查询体结构）
            query_type = self._determine_query_type(query.query_body)
            distribution["by_query_type"][query_type] = distribution["by_query_type"].get(query_type, 0) + 1
        
        return distribution
    
    def _determine_query_type(self, query_body: Dict[str, Any]) -> str:
        """确定查询类型"""
        if "aggs" in query_body:
            return "aggregation"
        elif query_body.get("query", {}).get("match"):
            return "match"
        elif query_body.get("query", {}).get("term"):
            return "term"
        elif query_body.get("query", {}).get("terms"):
            return "terms"
        elif query_body.get("query", {}).get("range"):
            return "range"
        elif query_body.get("query", {}).get("bool"):
            bool_query = query_body["query"]["bool"]
            if bool_query.get("must") or bool_query.get("should"):
                return "bool"
        return "other"
    
    def export_analysis_data(self) -> str:
        """导出分析数据为JSON格式"""
        with self._lock:
            data = [asdict(q) for q in self.query_history]
            return json.dumps(data, default=str, indent=2)
    
    def clear_history(self):
        """清空查询历史"""
        with self._lock:
            self.query_history.clear()
    
    def disable(self):
        """禁用分析器"""
        self.enabled = False
    
    def enable(self):
        """启用分析器"""
        self.enabled = True


# 全局查询分析器实例
query_analyzer = QueryAnalyzer()