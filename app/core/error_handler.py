#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理模块
"""

import traceback
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from functools import wraps
from flask import jsonify, request
from elasticsearch.exceptions import ConnectionError, RequestError, NotFoundError

logger = logging.getLogger(__name__)

class APIError(Exception):
    """API错误基类"""
    def __init__(self, message: str, code: int = 500, details: Optional[Dict] = None):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)

class ValidationError(APIError):
    """参数验证错误"""
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(message, 400, details)

class ESConnectionError(APIError):
    """ES连接错误"""
    def __init__(self, message: str = "Elasticsearch连接失败"):
        super().__init__(message, 503)

class ESQueryError(APIError):
    """ES查询错误"""
    def __init__(self, message: str, details: Optional[Dict] = None):
        super().__init__(f"查询失败: {message}", 500, details)

class ErrorHandler:
    """错误处理器"""
    
    @staticmethod
    def handle_api_error(error: APIError) -> tuple:
        """处理API错误"""
        logger.error(f"API错误: {error.message}", extra={
            'error_code': error.code,
            'error_details': error.details,
            'request_path': request.path if request else None,
            'request_method': request.method if request else None
        })
        
        response = {
            'success': False,
            'message': error.message,
            'error_code': error.code,
            'timestamp': datetime.now().isoformat()
        }
        
        if error.details:
            response['details'] = error.details
        
        return jsonify(response), error.code
    
    @staticmethod
    def handle_es_error(error: Exception) -> tuple:
        """处理ES相关错误"""
        if isinstance(error, ConnectionError):
            logger.error("ES连接错误", exc_info=True)
            return ErrorHandler.handle_api_error(
                ESConnectionError("无法连接到Elasticsearch服务")
            )
        
        elif isinstance(error, RequestError):
            logger.error(f"ES请求错误: {error}", exc_info=True)
            return ErrorHandler.handle_api_error(
                ESQueryError("查询语法错误", {'original_error': str(error)})
            )
        
        elif isinstance(error, NotFoundError):
            logger.error(f"ES索引不存在: {error}", exc_info=True)
            return ErrorHandler.handle_api_error(
                ESQueryError("索引不存在", {'original_error': str(error)})
            )
        
        else:
            logger.error(f"未知ES错误: {error}", exc_info=True)
            return ErrorHandler.handle_api_error(
                ESQueryError("未知错误", {'original_error': str(error)})
            )
    
    @staticmethod
    def handle_unexpected_error(error: Exception) -> tuple:
        """处理未预期的错误"""
        logger.error("未预期的错误", exc_info=True)
        
        response = {
            'success': False,
            'message': '服务器内部错误',
            'error_code': 500,
            'timestamp': datetime.now().isoformat()
        }
        
        # 开发模式下显示详细错误信息
        if logger.level == logging.DEBUG:
            response['debug_info'] = {
                'error_type': type(error).__name__,
                'error_message': str(error),
                'traceback': traceback.format_exc()
            }
        
        return jsonify(response), 500

def handle_errors(func):
    """错误处理装饰器"""
    @wraps(func)
    def error_wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except APIError as e:
            return ErrorHandler.handle_api_error(e)
        except (ConnectionError, RequestError, NotFoundError) as e:
            return ErrorHandler.handle_es_error(e)
        except Exception as e:
            return ErrorHandler.handle_unexpected_error(e)
    
    return error_wrapper

def validate_query_params(data: Dict[str, Any]) -> Dict[str, Any]:
    """验证查询参数"""
    errors = {}
    
    # 验证搜索关键词和ID不能同时为空
    search_keyword = data.get('searchKeyword', '').strip()
    id_search = data.get('idSearch', '').strip()
    
    if not search_keyword and not id_search:
        errors['search'] = '搜索关键词或ID不能同时为空'
    
    # 验证分页参数
    page = data.get('page', 1)
    page_size = data.get('pageSize', 50)
    cursor = data.get('cursor')
    
    # 如果提供了游标，则忽略页码参数
    if cursor:
        if not isinstance(cursor, str):
            errors['cursor'] = '游标必须是字符串'
    else:
        try:
            page = int(page)
            if page < 1:
                errors['page'] = '页码必须大于0'
        except (ValueError, TypeError):
            errors['page'] = '页码必须是有效数字'
        
        if page_size != 'all':
            try:
                page_size = int(page_size)
                if page_size < 1 or page_size > 1000:
                    errors['page_size'] = '每页大小必须在1-1000之间'
            except (ValueError, TypeError):
                errors['page_size'] = '每页大小必须是有效数字'
    
    # 验证内容来源过滤
    content_source = data.get('contentSourceFilter')
    if content_source and content_source not in ['files_v2', 'text']:
        errors['content_source'] = '内容来源必须是files_v2或text'
    
    # 验证排序参数
    order_by = data.get('orderBy', 'public_time DESC')
    valid_orders = ['public_time DESC', 'public_time ASC', '_score DESC', '_score ASC']
    if order_by not in valid_orders:
        errors['order_by'] = f'排序参数必须是: {", ".join(valid_orders)}'
    
    if errors:
        raise ValidationError('参数验证失败', errors)
    
    return {
        'searchKeyword': search_keyword,
        'idSearch': id_search,
        'page': page,
        'pageSize': page_size,
        'contentSourceFilter': content_source,
        'orderBy': order_by,
        'enableGrouping': data.get('enableGrouping', True),
        'dateRange': data.get('dateRange'),
        'cursor': cursor
    }