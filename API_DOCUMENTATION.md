# ES查询服务API文档

## 目录
1. [基础查询接口](#基础查询接口)
2. [查询模板接口](#查询模板接口)
3. [性能分析接口](#性能分析接口)
4. [其他接口](#其他接口)

## 基础查询接口

### POST /api/query
执行基础查询

**请求体:**
```json
{
  "searchKeyword": "关键词1,关键词2",
  "idSearch": "ID1,ID2",
  "orderBy": "public_time DESC",
  "page": 1,
  "pageSize": 50,
  "contentSourceFilter": "files_v2",
  "dateRange": {
    "start": "2023-01-01",
    "end": "2023-12-31"
  },
  "enableGrouping": true
}
```

**响应:**
```json
{
  "success": true,
  "message": "查询成功",
  "data": [...],
  "pagination": {...},
  "meta": {...}
}
```

## 查询模板接口

### GET /api/query-templates
获取所有查询模板列表

**响应:**
```json
{
  "success": true,
  "data": [
    {
      "template_id": "keyword_search_basic",
      "name": "基础关键词搜索",
      "description": "支持多关键词跨字段搜索的模板",
      "template_type": "keyword_search",
      "parameters": {...},
      "query_body": {...}
    }
  ]
}
```

### GET /api/query-template/<template_id>
获取指定查询模板详情

**响应:**
```json
{
  "success": true,
  "data": {
    "template_id": "keyword_search_basic",
    "name": "基础关键词搜索",
    "description": "支持多关键词跨字段搜索的模板",
    "template_type": "keyword_search",
    "parameters": {...},
    "query_body": {...}
  }
}
```

### POST /api/query-template
使用查询模板执行搜索

**请求体:**
```json
{
  "template_id": "keyword_search_basic",
  "template_params": {
    "keywords": ["关键词1", "关键词2"],
    "fields": ["files_v2.file_v2_original", "text"],
    "page": 1,
    "page_size": 50
  }
}
```

**响应:**
```json
{
  "success": true,
  "message": "查询成功",
  "data": [...],
  "pagination": {...}
}
```

## 性能分析接口

### GET /api/performance-report
获取查询性能报告

**响应:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "total_queries": 100,
      "avg_execution_time_ms": 45.2,
      "max_execution_time_ms": 120.5,
      "min_execution_time_ms": 12.3,
      "cache_hit_rate": 65.0,
      "error_rate": 2.0,
      "aggregation_query_rate": 15.0,
      "highlight_query_rate": 80.0
    },
    "slow_queries": [...],
    "query_distribution": {...}
  }
}
```

## 其他接口

### GET /api/statistics
获取搜索统计信息

**查询参数:**
- keyword: 搜索关键词

### POST /api/scroll-search
滚动搜索（处理大量数据）

**请求体:**
```json
{
  "searchKeyword": "关键词",
  "scrollSize": 1000
}
```